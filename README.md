# 📌 MobiArmy Server - Java + MySQL

👉 Đây là server **MobiArmy** được viết bằng **Java** và sử dụng **MySQL** để quản lý dữ liệu. Server hỗ trợ đầy đủ các tính năng kết nối, quản lý người chơi, và xử lý dữ liệu trong game.

## 🔹 Giới thiệu
- **MobiArmy Server** được xây dựng nhằm tái tạo lại hệ thống server của game MobiArmy 2.
- Hỗ trợ đầy đủ các chức năng của game như xử lý kết nối, quản lý nhân vật, phò<PERSON> đấu, vũ khí, và cơ chế chiến đấu.
- Server được viết bằng **Java 8+** và sử dụng **MySQL** để lưu trữ dữ liệu.

### 🛠 Công nghệ sử dụng
- **Java 8+**
- **MySQL** (<PERSON><PERSON> sở dữ liệu lưu trữ thông tin nhân vật, trận đấu,...)
- **NetBeans/IntelliJ IDEA** (Môi trường phát triển khuyến nghị)

## 📸 Hình ảnh minh họa
Dưới đây là một số hình ảnh về hệ thống server đang hoạt động:

### 🔹 Giao diện quản lý server
![Giao diện quản lý server](src/anh1.png)

### 🔹 Cấu hình kết nối MySQL
![Cấu hình MySQL](src/anh2.png)

### 🔹 Xử lý bot trong game
![Xử lý bot](src/anh3.png)

### 🔹 Server đang hoạt động
![Server đang hoạt động](src/anh4.png)

## 💡 Hướng dẫn cài đặt & chạy server

1️⃣ **Clone repository:**
```sh
git clone https://github.com/vantu03/MobiArmy2-Server.git
```

2️⃣ **Cấu hình server:**
- Sao chép file `config.example.properties` thành `config.properties`
- Chỉnh sửa các thông số trong file `config.properties` theo nhu cầu của bạn:

```properties
# Cổng server
server.port=8122

# Thông tin database
database.url=********************************
database.username=root
database.password=

# Cấu hình connection pool
database.pool.maximum=10
database.pool.minimum=5

# Cấu hình game
game.auto.generate.bot=true
game.max.bot.count=50
```

3️⃣ **Cấu hình MySQL:**
- Tạo database với tên `army` (hoặc tên khác theo cấu hình)
- Import file SQL để tạo các bảng cần thiết
- Cập nhật thông tin kết nối trong `config.properties`

4️⃣ **Chạy server:**
- Mở dự án bằng **NetBeans** hoặc **IntelliJ IDEA**
- Chạy file `MobiArmy.java` để khởi động server
- Server sẽ tự động đọc cấu hình từ file `config.properties`

5️⃣ **Kết nối client:**
- Sau khi server chạy, client có thể kết nối bằng cách nhập địa chỉ server và port
- Port được cấu hình trong file `config.properties` (mặc định: 8122)

🔗 **Tải Client tại đây:** [MobiArmy2 Client](https://github.com/vantu03/MobiArmy2-Client)

---

## ⚙️ Cấu hình nâng cao

### 📁 File cấu hình
Server sử dụng file `config.properties` để quản lý các cấu hình. Nếu không tìm thấy file này, server sẽ sử dụng cấu hình mặc định.

### 🔧 Các tham số cấu hình

| Tham số | Mô tả | Giá trị mặc định |
|---------|-------|------------------|
| `server.port` | Cổng server lắng nghe | 8122 |
| `database.url` | URL kết nối MySQL | ******************************** |
| `database.username` | Tên đăng nhập MySQL | root |
| `database.password` | Mật khẩu MySQL | (trống) |
| `database.pool.maximum` | Số kết nối tối đa trong pool | 10 |
| `database.pool.minimum` | Số kết nối tối thiểu trong pool | 5 |
| `database.connection.timeout` | Thời gian chờ kết nối (ms) | 30000 |
| `database.max.lifetime` | Thời gian sống tối đa kết nối (ms) | 1800000 |
| `game.auto.generate.bot` | Tự động tạo bot | true |
| `game.max.bot.count` | Số lượng bot tối đa | 50 |

### 📝 Lưu ý quan trọng
- Sau khi thay đổi cấu hình, cần **khởi động lại server**
- File `config.properties` phải đặt trong thư mục gốc của dự án
- Sử dụng file `config.example.properties` làm mẫu

---

💚 Nếu bạn có bất kỳ góp ý nào, hãy mở **Issues** hoặc tạo **Pull Request** để cải thiện dự án! 🚀

---

