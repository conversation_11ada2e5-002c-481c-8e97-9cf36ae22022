package mobiarmy.boss;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;
import mobiarmy.server.JsonUtil;
import mobiarmy.server.Server;

public class BossInfo {

    public static BossInfo[] entrys;

    public int id;
    public String name;
    public int[][] standing;
    public int[][] walking;
    public int[][] hit;
    public int[][] attack;
    public int[][] death;
    public int width;
    public int height;
    public int dx;
    public int dy;
    public int tick;
    public String folder;

    public BossInfo(int id, String name, int[][] standing, int[][] walking, int[][] hit, int[][] attack, int[][] death, int width, int height, int dx, int dy, int tick, String folder) {
        this.id = id;
        this.name = name;
        this.standing = standing;
        this.walking = walking;
        this.hit = hit;
        this.attack = attack;
        this.death = death;
        this.width = width;
        this.height = height;
        this.dx = dx;
        this.dy = dy;
        this.tick = tick;
        this.folder = folder;
    }

    public static void loadBossInfo() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM boss");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<BossInfo> tempList = new ArrayList<>();
            while (rs.next()) {
                int id = rs.getByte("id") + 29;
                String name = rs.getString("name");
                
                // Parse JSON strings to 2D int arrays
                int[][] standing = JsonUtil.fromJsonTo2DIntArray(rs.getString("standing"));
                int[][] walking = JsonUtil.fromJsonTo2DIntArray(rs.getString("walking"));
                int[][] hit = JsonUtil.fromJsonTo2DIntArray(rs.getString("hit"));
                int[][] attack = JsonUtil.fromJsonTo2DIntArray(rs.getString("attack"));
                int[][] death = JsonUtil.fromJsonTo2DIntArray(rs.getString("death"));
                
                int width = rs.getInt("width");
                int height = rs.getInt("height");
                int dx = rs.getInt("dx");
                int dy = rs.getInt("dy");
                int tick = rs.getInt("tick");
                String folder = rs.getString("folder");
                
                // Create BossInfo object
                BossInfo bossInfo = new BossInfo(id, name, standing, walking, hit, attack, death, width, height, dx, dy, tick, folder);
                tempList.add(bossInfo);
            }
            entrys = tempList.toArray(new BossInfo[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " boss info từ database");
    }

    public static BossInfo getByName(String bossName) {
        for (BossInfo bossInfo : entrys) {
            if (bossInfo.name.equals(bossName)) {
                return bossInfo;
            }
        }
        return null;
    }
}
