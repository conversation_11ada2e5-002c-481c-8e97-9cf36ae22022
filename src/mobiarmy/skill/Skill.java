package mobiarmy.skill;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;
import mobiarmy.server.Server;

public class Skill {

    public static Skill[] entrys;

    public int id;
    public String name;
    public String detail;
    public int iconId;

    public static void loadSkill() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM skill");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Skill> tempList = new ArrayList<>();
            while (rs.next()) {
                Skill skill = new Skill();
                skill.id = rs.getInt("id");
                skill.name = rs.getString("name");
                skill.detail = rs.getString("detail");
                skill.iconId = rs.getInt("iconid");
                tempList.add(skill);
            }
            entrys = tempList.toArray(new Skill[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " skills từ database");
    }
    public static Skill get(int id) {
        for (Skill entry : entrys) {
            if (entry.id == id) return entry;
        }
        return null;
    }
}
