package mobiarmy.ranking;

import mobiarmy.db.DBManager;
import mobiarmy.server.Glass;
import mobiarmy.server.User;
import mobiarmy.server.SessionManager;
import mobiarmy.server.Bot;
import mobiarmy.server.Exp;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;

/**
 * Class quản lý bảng xếp hạng với cache và update định kỳ
 */
public class RankingManager {

    private static final int PLAYERS_PER_PAGE = 10;
    private static final long CACHE_UPDATE_INTERVAL = 30 * 60 * 1000; // 30 phút

    // Cache lưu trữ dữ liệu ranking cho từng loại và trang
    private static final Map<String, List<RankingPlayer>> rankingCache = new ConcurrentHashMap<>();
    private static final Map<String, Long> cacheTimestamps = new ConcurrentHashMap<>();

    // Timer để update cache định kỳ
    private static Timer cacheUpdateTimer;
    private static boolean isInitialized = false;

    /**
     * Khởi tạo RankingManager và bắt đầu timer update cache
     */
    public static synchronized void initialize() {
        if (isInitialized) {
            return;
        }

        System.out.println("Initializing RankingManager with cache system...");

        // Load cache lần đầu
        updateAllRankingCache();

        // Khởi tạo timer để update cache định kỳ
        cacheUpdateTimer = new Timer("RankingCacheUpdater", true);
        cacheUpdateTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                try {
                    System.out.println("Updating ranking cache...");
                    updateAllRankingCache();
                    System.out.println("Ranking cache updated successfully");
                } catch (Exception e) {
                    System.err.println("Error updating ranking cache: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }, CACHE_UPDATE_INTERVAL, CACHE_UPDATE_INTERVAL);

        isInitialized = true;
        System.out.println("RankingManager initialized successfully");
    }

    /**
     * Dừng timer update cache
     */
    public static synchronized void shutdown() {
        if (cacheUpdateTimer != null) {
            cacheUpdateTimer.cancel();
            cacheUpdateTimer = null;
        }
        rankingCache.clear();
        cacheTimestamps.clear();
        isInitialized = false;
        System.out.println("RankingManager shutdown completed");
    }

    /**
     * Lấy danh sách người chơi theo loại bảng xếp hạng từ cache
     */
    public static List<RankingPlayer> getRankingPlayers(RankingType type, int page) {
        // Đảm bảo RankingManager đã được khởi tạo
        if (!isInitialized) {
            initialize();
        }

        String cacheKey = getCacheKey(type, page);
        List<RankingPlayer> cachedData = rankingCache.get(cacheKey);

        // Nếu có cache và chưa hết hạn thì trả về cache
        if (cachedData != null && isCacheValid(cacheKey)) {
            return new ArrayList<>(cachedData); // Trả về copy để tránh modification
        }

        // Nếu không có cache hoặc cache đã hết hạn, load từ database
        List<RankingPlayer> players = loadRankingPlayersFromDB(type, page);

        // Lưu vào cache
        rankingCache.put(cacheKey, players);
        cacheTimestamps.put(cacheKey, System.currentTimeMillis());

        return new ArrayList<>(players);
    }

    /**
     * Load dữ liệu ranking từ database (method cũ được rename)
     */
    private static List<RankingPlayer> loadRankingPlayersFromDB(RankingType type, int page) {
        List<RankingPlayer> players = new ArrayList<>();

        try (Connection connection = DBManager.getInstance().getConnection();
             PreparedStatement statement = connection.prepareStatement(buildRankingQuery(type))) {

            statement.setInt(1, page * PLAYERS_PER_PAGE);
            statement.setInt(2, PLAYERS_PER_PAGE);

            try (ResultSet rs = statement.executeQuery()) {
                int rank = page * PLAYERS_PER_PAGE + 1;

                while (rs.next()) {
                    RankingPlayer player = new RankingPlayer();
                    player.rank = rank++;
                    player.id = rs.getInt("user_id");
                    player.name = rs.getString("name");
                    player.xu = rs.getLong("xu");
                    player.luong = rs.getLong("luong");
                    player.cup = rs.getInt("cup");
                    player.clan = rs.getShort("clan");

                    // Lấy thông tin level từ glasses JSON
                    String glassesJson = rs.getString("glasses");
                    byte selectedGlass = rs.getByte("selected_glass");

                    // Cho ranking cao thủ, lấy max level thay vì selected glass level
                    if (type == RankingType.CAO_THU) {
                        player.level = parseMaxGlassLevel(glassesJson);
                        player.levelPercent = parseGlassLevelPercent(glassesJson, selectedGlass);
                    } else {
                        player.level = parseGlassLevel(glassesJson, selectedGlass);
                        player.levelPercent = parseGlassLevelPercent(glassesJson, selectedGlass);
                    }

                    player.selectGlass = selectedGlass;
                    player.equipIds = parseEquipIds(glassesJson, selectedGlass);

                    // Tạo chuỗi hiển thị giá trị ranking
                    player.displayValue = getRankingDisplayValue(type, player);

                    players.add(player);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return players;
    }

    /**
     * Update toàn bộ cache cho tất cả loại ranking và trang đầu tiên
     */
    private static void updateAllRankingCache() {
        for (RankingType type : RankingType.values()) {
            // Update cache cho 5 trang đầu tiên của mỗi loại ranking
            for (int page = 0; page < 5; page++) {
                try {
                    List<RankingPlayer> players = loadRankingPlayersFromDB(type, page);
                    String cacheKey = getCacheKey(type, page);
                    rankingCache.put(cacheKey, players);
                    cacheTimestamps.put(cacheKey, System.currentTimeMillis());
                } catch (Exception e) {
                    System.err.println("Error updating cache for " + type + " page " + page + ": " + e.getMessage());
                }
            }
        }
    }

    /**
     * Tạo cache key từ type và page
     */
    private static String getCacheKey(RankingType type, int page) {
        return type.name() + "_" + page;
    }

    /**
     * Kiểm tra cache có còn hợp lệ không
     */
    private static boolean isCacheValid(String cacheKey) {
        Long timestamp = cacheTimestamps.get(cacheKey);
        if (timestamp == null) {
            return false;
        }
        return (System.currentTimeMillis() - timestamp) < CACHE_UPDATE_INTERVAL;
    }

    /**
     * Force update cache cho một loại ranking cụ thể
     */
    public static void forceUpdateCache(RankingType type) {
        for (int page = 0; page < 5; page++) {
            try {
                List<RankingPlayer> players = loadRankingPlayersFromDB(type, page);
                String cacheKey = getCacheKey(type, page);
                rankingCache.put(cacheKey, players);
                cacheTimestamps.put(cacheKey, System.currentTimeMillis());
            } catch (Exception e) {
                System.err.println("Error force updating cache for " + type + " page " + page + ": " + e.getMessage());
            }
        }
    }

    /**
     * Xây dựng câu query SQL theo loại bảng xếp hạng
     */
    private static String buildRankingQuery(RankingType type) {
        String baseQuery = "SELECT user_id, name, xu, luong, cup, clan, selected_glass, glasses FROM players WHERE 1=1";

        switch (type) {
            case DANH_DU:
                return baseQuery + " ORDER BY cup DESC LIMIT ?, ?";
            case CAO_THU:
                // Sắp xếp theo level cao nhất sử dụng MySQL JSON functions
                return """
                    SELECT user_id, name, xu, luong, cup, clan, selected_glass, glasses,
                           COALESCE((
                               SELECT MAX(CAST(JSON_EXTRACT(glass_item, '$.level') AS UNSIGNED))
                               FROM JSON_TABLE(glasses, '$[*]' 
                                   COLUMNS (glass_item JSON PATH '$')
                               ) jt
                           ), 1) as max_level
                    FROM players 
                    WHERE glasses IS NOT NULL AND glasses != '[]'
                    ORDER BY max_level DESC, cup DESC 
                    LIMIT ?, ?
                    """;
            case DAI_GIA_XU:
                return baseQuery + " ORDER BY xu DESC LIMIT ?, ?";
            case DAI_GIA_LUONG:
                return baseQuery + " ORDER BY luong DESC LIMIT ?, ?";
            default:
                return baseQuery + " ORDER BY cup DESC LIMIT ?, ?";
        }
    }

    /**
     * Parse level từ glasses JSON cho selected glass
     */
    public static int parseGlassLevel(String glassesJson, byte selectedGlass) {
        try {
            if (glassesJson == null || glassesJson.isEmpty() || glassesJson.equals("[]")) {
                return 1;
            }

            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
            if (glassesArray != null) {
                for (int i = 0; i < glassesArray.size(); i++) {
                    JSONObject glassObj = (JSONObject) glassesArray.get(i);
                    byte glassID = Byte.parseByte(glassObj.get("glassID").toString());

                    if (glassID == selectedGlass) {
                        int exp = Integer.parseInt(glassObj.get("exp").toString());
                        return Exp.getLevelExp(exp);
                    }
                }
            }
            return 1;
        } catch (Exception e) {
            return 1;
        }
    }

    /**
     * Parse level cao nhất từ tất cả glasses
     */
    public static int parseMaxGlassLevel(String glassesJson) {
        try {
            if (glassesJson == null || glassesJson.isEmpty() || glassesJson.equals("[]")) {
                return 1;
            }

            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
            if (glassesArray != null) {
                int maxLevel = 1;
                for (int i = 0; i < glassesArray.size(); i++) {
                    JSONObject glassObj = (JSONObject) glassesArray.get(i);

                    // Có thể glass object có level trực tiếp hoặc exp cần convert
                    int level;
                    if (glassObj.containsKey("level")) {
                        level = Integer.parseInt(glassObj.get("level").toString());
                    } else if (glassObj.containsKey("exp")) {
                        int exp = Integer.parseInt(glassObj.get("exp").toString());
                        level = Exp.getLevelExp(exp);
                    } else {
                        continue;
                    }

                    if (level > maxLevel) {
                        maxLevel = level;
                    }
                }
                return maxLevel;
            }
            return 1;
        } catch (Exception e) {
            return 1;
        }
    }

    /**
     * Parse equip IDs từ selected glass
     */
    public static short[] parseEquipIds(String glassesJson, byte selectedGlass) {
        try {
            if (glassesJson == null || glassesJson.isEmpty() || glassesJson.equals("[]")) {
                return new short[]{0, 0, 0, 0, 0};
            }

            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
            if (glassesArray != null) {
                for (int i = 0; i < glassesArray.size(); i++) {
                    JSONObject glassObj = (JSONObject) glassesArray.get(i);
                    byte glassID = Byte.parseByte(glassObj.get("glassID").toString());

                    if (glassID == selectedGlass) {
                        JSONArray equipArray = (JSONArray) glassObj.get("equipID");
                        short[] equipIds = new short[equipArray.size()];
                        for (int j = 0; j < equipArray.size(); j++) {
                            equipIds[j] = Short.parseShort(equipArray.get(j).toString());
                        }
                        return equipIds;
                    }
                }
            }
            return new short[]{0, 0, 0, 0, 0};
        } catch (Exception e) {
            return new short[]{0, 0, 0, 0, 0};
        }
    }

    /**
     * Parse level percent từ glasses JSON
     */
    public static int parseGlassLevelPercent(String glassesJson, byte selectedGlass) {
        try {
            if (glassesJson == null || glassesJson.isEmpty() || glassesJson.equals("[]")) {
                return 0;
            }

            JSONArray glassesArray = (JSONArray) JSONValue.parse(glassesJson);
            if (glassesArray != null) {
                for (int i = 0; i < glassesArray.size(); i++) {
                    JSONObject glassObj = (JSONObject) glassesArray.get(i);
                    byte glassID = Byte.parseByte(glassObj.get("glassID").toString());

                    if (glassID == selectedGlass) {
                        int level = Integer.parseInt(glassObj.get("level").toString());
                        int exp = Integer.parseInt(glassObj.get("exp").toString());
                        return Exp.getPercentExp(level, exp);
                    }
                }
            }
            return 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * Lấy giá trị hiển thị cho ranking
     */
    private static String getRankingDisplayValue(RankingType type, RankingPlayer player) {
        switch (type) {
            case DANH_DU:
                return String.valueOf(player.cup);
            case CAO_THU:
                return "Lv." + player.level;
            case DAI_GIA_XU:
                return formatNumber(player.xu) + " xu";
            case DAI_GIA_LUONG:
                return formatNumber(player.luong) + " lượng";
            default:
                return "";
        }
    }

    /**
     * Format số với đơn vị K, M
     */
    private static String formatNumber(long number) {
        if (number >= 1000000) {
            return (number / 1000000) + "M";
        } else if (number >= 1000) {
            return (number / 1000) + "K";
        }
        return String.valueOf(number);
    }

    /**
     * Class chứa thông tin người chơi trong bảng xếp hạng
     */
    public static class RankingPlayer {
        public int rank;
        public int id;
        public String name;
        public long xu;
        public long luong;
        public byte selectGlass;
        public int cup;
        public short clan;
        public int level;
        public int levelPercent;
        public short[] equipIds;
        public String displayValue;
    }
}