package mobiarmy.ranking;

/**
 * Enum định nghĩa các loại bảng xếp hạng
 */
public enum RankingType {
    DANH_DU(0, "DANH DỰ", "cup"),
    CAO_THU(1, "CAO THỦ", "level"),
    DAI_GIA_XU(2, "ĐẠI GIA XU", "xu"),
    DAI_GIA_LUONG(3, "ĐẠI GIA LƯỢNG", "luong");
    
    private final int id;
    private final String displayName;
    private final String orderField;
    
    RankingType(int id, String displayName, String orderField) {
        this.id = id;
        this.displayName = displayName;
        this.orderField = orderField;
    }
    
    public int getId() {
        return id;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getOrderField() {
        return orderField;
    }
    
    public static RankingType getById(int id) {
        for (RankingType type : values()) {
            if (type.id == id) {
                return type;
            }
        }
        return DANH_DU; // Default
    }
    
    public static String[] getAllDisplayNames() {
        String[] names = new String[values().length];
        for (int i = 0; i < values().length; i++) {
            names[i] = values()[i].displayName;
        }
        return names;
    }
}
