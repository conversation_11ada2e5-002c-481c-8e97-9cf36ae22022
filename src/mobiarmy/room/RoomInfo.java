package mobiarmy.room;

import mobiarmy.db.DBManager;
import mobiarmy.server.JsonUtil;
import org.json.simple.JSONArray;
import org.json.simple.JSONValue;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import mobiarmy.server.Server;

/**
 *
 * <AUTHOR> Tú
 */
public class RoomInfo {

    public byte type;
    public byte id;
    public byte lv;
    public String name = "";
    public String playerMax = "";
    public byte roomFree;
    public byte roomWait;
    public int stat;
    public byte[] map;
    public ArrayList<RoomWait> roomWaits;

    public static RoomInfo[] entrys;

    public static class RoomInfoName {
        public int start;
        public int index;
        public String name;

        public RoomInfoName(int start, int index, String name) {
            this.start = start;
            this.index = index;
            this.name = name;
        }
    }

    public static final ArrayList<RoomInfo> rooms = new ArrayList<>();

    public static final ArrayList<RoomInfoName> roomNames = new ArrayList<>();
    public static HashMap<String,List<RoomWait>> allRoomWaits = new HashMap<>();

    public static void loadRoomInfo() throws SQLException {
        Connection connection =DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM room_info ORDER BY type ASC");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<RoomInfo> tempList = new ArrayList<>();
            rooms.clear();
            roomNames.clear();
            ArrayList<Byte> types = new ArrayList<>();
            int i = 0;
            while (rs.next()) {
                RoomInfo roomInfo = new RoomInfo();
                roomInfo.type = rs.getByte("type");
                roomInfo.id = (byte) i;
                roomInfo.name = rs.getString("name");
                // Parse map byte array
                String mapJson = rs.getString("map");
                if (mapJson != null && !mapJson.trim().isEmpty() && !mapJson.equals("null")) {
                    JSONArray mapArray = (JSONArray) JSONValue.parse(mapJson);
                    if (mapArray != null) {
                        roomInfo.map = new byte[mapArray.size()];
                        for (int j = 0; j < mapArray.size(); j++) {
                            roomInfo.map[j] = Byte.parseByte(mapArray.get(j).toString());
                        }
                    }
                }
                roomInfo.roomWait = rs.getByte("roomWait");
                roomInfo.roomWaits = new ArrayList<>();
                for (int j = 0; j < roomInfo.roomWait; j++) {
                    roomInfo.roomWaits.add(new RoomWait(
                            roomInfo.id,
                            roomInfo.type,
                            (byte) j,
                            roomInfo.map,
                            rs.getInt("minMoney"),
                            rs.getInt("maxMoney"),
                            rs.getByte("maxPlayer")));
                }
                tempList.add(roomInfo);
                rooms.add(roomInfo);
                if (!types.contains(roomInfo.type)) {
                    types.add(roomInfo.type);
                }
                if (roomInfo.name != null) {
                    roomNames.add(new RoomInfoName(types.size() > 1 ? types.size() - 1 : 0, i, roomInfo.name));
                }
                i++;
            }
            entrys = tempList.toArray(new RoomInfo[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        
        for (Room room : Room.entrys) {
            allRoomWaits.put(room.name, rooms.stream()
                .filter(roomInfo -> roomInfo.type == room.type)
                .flatMap(roomInfo -> roomInfo.roomWaits.stream())
                .collect(Collectors.toList()));
        // Remove rooms with no room waits
        }
        allRoomWaits.entrySet().removeIf(entry -> entry.getValue().isEmpty());
        // Sort allRoomWaits by type
        allRoomWaits = allRoomWaits.entrySet().stream()
            .sorted((entry1, entry2) -> {
                // Get the type of the first room wait in each list
                byte type1 = entry1.getValue().isEmpty() ? 0 : entry1.getValue().get(0).type;
                byte type2 = entry2.getValue().isEmpty() ? 0 : entry2.getValue().get(0).type;
                return Byte.compare(type1, type2);
            })
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (e1, e2) -> e1,
                LinkedHashMap::new
            ));
        
        System.out.println("✅ Đã load " + entrys.length + " room info từ database");
    }

    public static RoomInfo get(byte id) {
        for (RoomInfo roomInfo : entrys) {
            if (roomInfo.id == id) {
                return roomInfo;
            }
        }
        return null;
    }

    public static RoomWait get(byte roomID, byte boardID) {
        for (RoomInfo roomInfo : entrys) {
            if (roomInfo.id == roomID) {
                for (RoomWait roomWait : roomInfo.roomWaits) {
                    if (roomWait.boardID == boardID) {
                        return roomWait;
                    }
                }
            }
        }
        return null;
    }

    public static void update() {
        for (RoomInfo roomInfo : entrys) {
            int nPlayer = 0;
            int nMax = 0;
            for (RoomWait wait : roomInfo.roomWaits) {
                if (!wait.started) {
                    nPlayer += wait.numPlayer;
                } else {
                    nPlayer += wait.playerLimit;
                }
                nMax += wait.playerLimit;
                if (!wait.lock) {
                    wait.update();
                }
            }
            int percent = (int) (((float) nPlayer / (float) nMax) * 100F);
            roomInfo.stat = percent > 70 ? 0 : percent > 50 ? 1 : 2;
        }
    }

    public int getFullRoom() {
        int fullRoom = 0;
        for (RoomInfo room : RoomInfo.rooms) {
            fullRoom += room.roomWaits.size();
        }
        return fullRoom;
    }

}
