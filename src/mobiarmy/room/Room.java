package mobiarmy.room;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;
import mobiarmy.server.Server;

/**
 *
 * <AUTHOR> Tú
 */
public class Room {
    
    public byte type;
    public String name;
    
    public ArrayList<RoomInfo> roomInfos;
    
    public static final byte MAX_E_PLAYER = 100;
    public static final byte MAX_PLAYER = 8;
    public static final int INDEX_START = 8;
    
    public static class RoomBoss {
        
        public int mapId;
        public int glass;
        
        public RoomBoss(int mapId, int glass) {
            this.mapId = mapId;
            this.glass = glass;
        }
        
    }
    
    public static Room[] entrys;
    
    
    public static void loadRoom() throws SQLException {
        Connection connection =DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM room");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Room> tempList = new ArrayList<>();
            while (rs.next()) {
                Room room = new Room();
                room.type = rs.getByte("type");
                room.name = rs.getString("name");
                tempList.add(room);
            }
            entrys = tempList.toArray(new Room[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " rooms từ database");
    }
    
}
