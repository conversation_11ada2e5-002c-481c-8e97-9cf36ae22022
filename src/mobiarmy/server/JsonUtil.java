package mobiarmy.server;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import java.util.Base64;

/**
 * Simplified utility class for JSON operations using JSON Simple
 * Provides basic conversion methods for commonly used data types
 */
public class JsonUtil {
    
    /**
     * Simple parse method - use JSONValue.parse() directly for most cases
     */
    public static Object parse(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        return JSONValue.parse(json);
    }
    
    /**
     * Legacy method - prefer using JSONValue.parse() and casting directly
     */
    @SuppressWarnings("unchecked")
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        
        Object parsed = JSONValue.parse(json);
        if (parsed != null && clazz.isAssignableFrom(parsed.getClass())) {
            return (T) parsed;
        }
        
        System.err.println("Warning: Use JSONValue.parse() and cast directly instead of fromJson()");
        return null;
    }
    
    /**
     * Legacy method for complex object arrays - prefer manual parsing with JSONValue.parse()
     * Example: JSONArray arr = (JSONArray) JSONValue.parse(json);
     */
    public static <T> T[] fromJsonArray(String json, Class<T> elementType) {
        System.err.println("Warning: fromJsonArray is deprecated. Use JSONValue.parse() and manual conversion instead.");
        return null;
    }
    
    /**
     * Parse JSON to short array - commonly used in the codebase
     * Example: short[] arr = JsonUtil.fromJsonToShortArray("[1,2,3]");
     */
    public static short[] fromJsonToShortArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        short[] result = new short[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            if (item instanceof Number) {
                result[i] = ((Number) item).shortValue();
            } else {
                result[i] = Short.parseShort(item.toString());
            }
        }
        return result;
    }
    
    /**
     * Parse JSON to int array - commonly used in the codebase
     */
    public static int[] fromJsonToIntArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        int[] result = new int[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            result[i] = item instanceof Number ? ((Number) item).intValue() : Integer.parseInt(item.toString());
        }
        return result;
    }
    
    /**
     * Parse JSON to byte array - handles both JSON arrays and Base64 strings
     */
    public static byte[] fromJsonToByteArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        
        Object parsed = JSONValue.parse(json);
        
        // Handle Base64 string (common for inv_ability, inv_percen)
        if (parsed instanceof String) {
            try {
                return Base64.getDecoder().decode((String) parsed);
            } catch (IllegalArgumentException e) {
                System.err.println("Error decoding Base64: " + e.getMessage());
                return null;
            }
        }
        
        // Handle JSON array
        if (parsed instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) parsed;
            byte[] result = new byte[jsonArray.size()];
            for (int i = 0; i < jsonArray.size(); i++) {
                Object item = jsonArray.get(i);
                result[i] = item instanceof Number ? ((Number) item).byteValue() : Byte.parseByte(item.toString());
            }
            return result;
        }
        
        return null;
    }
    
    /**
     * Parse JSON to Integer array - for friends list
     */
    public static Integer[] fromJsonToIntegerArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        Integer[] result = new Integer[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            result[i] = item instanceof Number ? ((Number) item).intValue() : Integer.parseInt(item.toString());
        }
        return result;
    }
    
    /**
     * Parse JSON to String array - commonly used for formula details
     */
    public static String[] fromJsonToStringArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        String[] result = new String[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            result[i] = item != null ? item.toString() : null;
        }
        return result;
    }
    
    /**
     * Parse JSON to 2D int array - for boss animation frames
     */
    public static int[][] fromJsonTo2DIntArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        
        JSONArray outerArray = (JSONArray) JSONValue.parse(json);
        if (outerArray == null) return null;
        
        int[][] result = new int[outerArray.size()][];
        for (int i = 0; i < outerArray.size(); i++) {
            JSONArray innerArray = (JSONArray) outerArray.get(i);
            if (innerArray != null) {
                result[i] = new int[innerArray.size()];
                for (int j = 0; j < innerArray.size(); j++) {
                    Object item = innerArray.get(j);
                    result[i][j] = item instanceof Number ? ((Number) item).intValue() : Integer.parseInt(item.toString());
                }
            }
        }
        return result;
    }
    
    /**
     * Simple toJson for basic arrays - for backward compatibility
     * For complex objects, prefer manual JSONObject/JSONArray creation
     */
    @SuppressWarnings("unchecked")
    public static String toJson(Object obj) {
        if (obj == null) return "null";
        
        if (obj instanceof JSONObject || obj instanceof JSONArray) {
            return obj.toString();
        }
        
        // Handle simple arrays
        if (obj.getClass().isArray()) {
            JSONArray jsonArray = new JSONArray();
            int length = java.lang.reflect.Array.getLength(obj);
            for (int i = 0; i < length; i++) {
                Object item = java.lang.reflect.Array.get(obj, i);
                if (item instanceof byte[]) {
                    // Encode byte arrays as Base64
                    jsonArray.add(Base64.getEncoder().encodeToString((byte[]) item));
                } else {
                    jsonArray.add(item);
                }
            }
            return jsonArray.toJSONString();
        }
        
        // For complex objects, recommend manual creation
        System.err.println("Warning: Use JSONObject/JSONArray directly for complex objects");
        return "\"" + obj.toString() + "\"";
    }
}
