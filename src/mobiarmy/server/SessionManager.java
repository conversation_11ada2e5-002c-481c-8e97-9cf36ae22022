package mobiarmy.server;

import mobiarmy.db.DBManager;
import mobiarmy.server.JsonUtil;
import mobiarmy.util.Log;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;

/**
 *
 * <AUTHOR> Tú
 */
public class SessionManager {

    // Sử dụng HashMap để lưu trữ session, user và character
    private static final ArrayList<Session> sessions = new ArrayList<>();
    private static final ArrayList<User> users = new ArrayList<>();
    private static final HashMap<Integer, User> users_id = new HashMap<>();
    private static final HashMap<String, User> users_name = new HashMap<>();

    // Đối tượng khóa cho các thao tác đồng bộ hóa
    private static final Object lock = new Object();

    // Thêm session mới
    public static void addSession(Session session) {
        synchronized (lock) {
            sessions.add(session);
        }
    }

    // Thêm user mới
    public static void addUser(User user) {
        synchronized (lock) {
            users_id.put(user.id, user);
            users_name.put(user.name, user);
            users.add(user);
        }
    }

    public static ArrayList<User> generateUsers() {
        synchronized (lock) {
            return new ArrayList<>(users);
        }
    }

    // Xóa session theo ID
    public static void removeSession(Session session) {
        synchronized (lock) {
            sessions.remove(session);
        }
    }

    // Xóa user theo ID hoặc username
    public static void removeUserById(int id) {
        synchronized (lock) {
            User user = users_id.remove(id);
            if (user != null) {
                users_name.remove(user.name);
                users.remove(user);
            }
        }
    }

    public static void removeUserByName(String username) {
        synchronized (lock) {
            User user = users_name.remove(username);
            if (user != null) {
                users_id.remove(user.id);
                users.remove(user);
            }
        }
    }

    // Tìm session theo ID
    public static Session findSessionById(int sessionId) {
        synchronized (lock) {
            return sessions.get(sessionId);
        }
    }

    // Tìm user theo ID hoặc username
    public static User findUserById(int userId) {
        synchronized (lock) {
            return users_id.get(userId);
        }
    }

    public static User findUserByName(String username) {
        synchronized (lock) {
            return users_name.get(username);
        }
    }

    // Phương thức trả về kích thước của sessions
    public static int getSessionsSize() {
        return sessions.size();
    }

    // Phương thức trả về kích thước của users_id
    public static int getUsersIdSize() {
        return users_id.size();
    }

    // Phương thức trả về kích thước của users_username
    public static int getUsersUsernameSize() {
        return users_name.size();
    }


    public static void saveUsers() throws SQLException {
        // Make a copy of all users from SessionManager to avoid thread blockage
        ArrayList<User> usersCopy;

        synchronized (lock) {
            // Copy all users to prevent blocking the main thread
            usersCopy = new ArrayList<>(SessionManager.users);
        }

        // Iterate through the copied user list and save each user to the database
        for (User user : usersCopy) {
            try {
                user.saveData();
            } catch (Exception e) {
                Log.error("Lỗi saveUsers: " + e.getMessage());
            }
        }
    }

    public static void messageWorld(String str) {
        ArrayList<Session> list;
        synchronized (lock) {
            list = new ArrayList<>(sessions);
        }
        for (Session s : list) {
            s.sessionHandler.messageWorld(str);
        }
    }

    // =====================================================
    // JSON DATA CLASSES FOR DATABASE STORAGE
    // =====================================================

    // =====================================================
    // HELPER METHODS FOR DATA PARSING
    // =====================================================

    /**
     * Parse data field which can be:
     * - null
     * - "null" string
     * - JSON array string like "[17, 15, 16, -1, 18]"
     */
    public static short[] parseDataField(String dataStr) {
        if (dataStr == null || dataStr.equals("null") || dataStr.trim().isEmpty()) {
            return null;
        }

        try {
            // Try to parse as JSON array
            JSONArray jsonArray = (JSONArray) JSONValue.parse(dataStr);
            if (jsonArray != null) {
                short[] result = new short[jsonArray.size()];
                for (int i = 0; i < jsonArray.size(); i++) {
                    result[i] = Short.parseShort(jsonArray.get(i).toString());
                }
                return result;
            }
            return null;
        } catch (Exception e) {
            System.err.println("Error parsing data field: " + dataStr + " - " + e.getMessage());
            return null;
        }
    }

    public static void removeUser(User user) {
        synchronized (lock) {
            users_id.remove(user.id);
            users_name.remove(user.name);
            users.remove(user);
        }
    }

}
