package mobiarmy.server;

import mobiarmy.db.DBManager;
import mobiarmy.util.Util;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.sql.*;

/**
 * Created by khiem on 9/21/2025.
 *
 * <AUTHOR>
 */
public class GiftCode {
    private static final GiftCode instance = new GiftCode();

    public static GiftCode getInstance() {
        return instance;
    }

    public void use(User player, String code) {
        try {
            int lent = code.length();
            if (code.equals("") || lent < 5 || lent > 30) {
                player.session.sessionHandler.log("Mã quà tặng có chiều dài từ 5 đến 30 ký tự.");
                return;
            }

            Connection conn = DBManager.getInstance().getConnection(DBManager.GIFT_CODE);
            PreparedStatement stmt = conn.prepareStatement(
                    "SELECT * FROM `gift_codes` WHERE `code` = ? AND (expires_at IS NULL OR expires_at > now()) LIMIT 1;", ResultSet.TYPE_SCROLL_SENSITIVE, ResultSet.CONCUR_UPDATABLE);
            stmt.setString(1, code);
            ResultSet res = stmt.executeQuery();
            try {
                if (!res.first()) {
                    player.session.sessionHandler.log("Mã quà tặng không tồn tại hoặc đã hết hạn.");
                    return;
                }

                int id = res.getInt("id");
                byte status = res.getByte("status");
                byte type = res.getByte("type");
                byte used = res.getByte("used");
                if (status == 1) {
                    player.session.sessionHandler.log("Mã quà tặng đã được sử dụng");
                    return;
                } else if (type == 1 && isUsedGiftCode(player, code)) {
                    player.session.sessionHandler.log("Mỗi người chỉ được sử dụng 1 lần.");
                    return;
                }else if(type == 1 && used == 0 ){
                    player.session.sessionHandler.log("Đã hết số lần sử dụng mã quà tặng này.");
                    return;
                }
                int gold = res.getInt("gold");
                int exp = res.getInt("exp");
                int coin = res.getInt("coin");

                JSONArray arrItem = (JSONArray) (new JSONParser().parse(res.getString("items")));

                int size = arrItem.size();
//
//                if (size > player.getSlotNull()) {
//                    player.session.sessionHandler.log("Bạn không đủ chỗ trống trong hành trang.");
//                    return;
//                }
                StringBuilder sb = new StringBuilder();
                sb.append("Chúc mừng, bạn đã được tặng").append("\n\n");

                if (gold > 0) {
                    player.addLuong(gold, true);
                    sb.append(String.format("- %s lượng", Util.getCurrency(gold))).append("\n");
                }

                if (exp > 0) {
                    player.glass().addExp(exp, true);
                    sb.append(String.format("- %s exp", Util.getCurrency(exp))).append("\n");
                }

                if (coin > 0) {
                    player.addXu(coin, true);
                    sb.append(String.format("- %s xu", Util.getCurrency(coin))).append("\n");
                }

                for (int i = 0; i < size; i++) {
                    JSONObject itemObj = (JSONObject) arrItem.get(i);
                    int itemId = ((Long) itemObj.get("id")).intValue();
                    int num = ((Long) itemObj.get("num")).intValue();
                    player.addLinhTinh(itemId, num);
                    sb.append(
                                    String.format("- x%s %s", Util.getCurrency(num), LinhTinh.get(itemId).name))
                            .append("\n");
                }

//                player.user.session.addUseGiftCode();

//                player.getService().showAlert("Mã quà tặng", sb.toString());
                player.session.sessionHandler.log(sb.toString());
                addUsedGiftCode(player, code);


                if(used > 0 && type == 1){
                    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                    res.updateByte("used", (byte) (used -1));
                    res.updateTimestamp("updated_at", timestamp);
                    res.updateRow();
                }else if (type == 0) {
                    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                    res.updateByte("status", (byte) 1);
                    res.updateTimestamp("updated_at", timestamp);
                    res.updateRow();
                }

            } finally {
                conn.close();
                res.close();
                stmt.close();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isUsedGiftCode(User player, String giftCode) {
        try {
            PreparedStatement stmt = DBManager.getInstance().getConnection(DBManager.GIFT_CODE).prepareStatement(
                    "SELECT * FROM `gift_code_histories` WHERE `gift_code` = ? AND (`user_id` = ?) LIMIT 1;", ResultSet.TYPE_SCROLL_SENSITIVE,
                    ResultSet.CONCUR_READ_ONLY);
            stmt.setString(1, giftCode);
            stmt.setInt(2, player.id);
            ResultSet res = stmt.executeQuery();
            try {
                if (res.first()) {
                    return true;
                }
            } finally {
                res.close();
                stmt.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    public void addUsedGiftCode(User player, String giftCode) {
        try {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            PreparedStatement stmt = DBManager.getInstance().getConnection(DBManager.GIFT_CODE).prepareStatement("INSERT INTO `gift_code_histories`(`user_id`, `gift_code`, `updated_at`) VALUES (?, ?, ?)");
            stmt.setInt(1, player.id);
            stmt.setString(2, giftCode);
            stmt.setTimestamp(3, timestamp);
            stmt.executeUpdate();
            stmt.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
