package mobiarmy.server;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Quản lý cấu hình server từ file config.properties
 * 
 * <AUTHOR> Tú
 */
public class ConfigManager {
    
    private static final String CONFIG_FILE = "config.properties";
    private static Properties properties;
    private static ConfigManager instance;
    
    // Cached configuration values for performance
    private int cachedServerPort;
    private String cachedDatabaseUrl;
    private String cachedDatabaseUsername;
    private String cachedDatabasePassword;
    private int cachedDatabasePoolMaximum;
    private int cachedDatabasePoolMinimum;
    private long cachedDatabaseConnectionTimeout;
    private long cachedDatabaseMaxLifetime;
    private boolean cachedAutoGenerateBot;
    private int cachedMaxBotCount;
    
    // Cached resource versions
    private int cachedIconVersion;
    private int cachedMapVersion;
    private int cachedLayerVersion;
    private int cachedDataVersion;
    private int cachedCaptionVersion;
    
    // Singleton pattern
    private ConfigManager() {
        loadConfig();
        cacheAllConfiguration();
    }
    
    public static ConfigManager getInstance() {
        if (instance == null) {
            instance = new ConfigManager();
        }
        return instance;
    }
    
    /**
     * Tải cấu hình từ file config.properties
     */
    private void loadConfig() {
        properties = new Properties();
        
        try {
            // Thử đọc từ file trong thư mục gốc dự án
            FileInputStream fileInput = new FileInputStream(CONFIG_FILE);
            properties.load(fileInput);
            fileInput.close();
            System.out.println("✅ Đã tải cấu hình từ file: " + CONFIG_FILE);
            
        } catch (IOException e) {
            // Nếu không tìm thấy file, thử đọc từ resources
            try {
                InputStream resourceInput = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE);
                if (resourceInput != null) {
                    properties.load(resourceInput);
                    resourceInput.close();
                    System.out.println("✅ Đã tải cấu hình từ resources: " + CONFIG_FILE);
                } else {
                    throw new IOException("Không tìm thấy file cấu hình");
                }
            } catch (IOException ex) {
                System.err.println("❌ Không thể tải file cấu hình: " + CONFIG_FILE);
                System.err.println("❌ Sử dụng cấu hình mặc định...");
                loadDefaultConfig();
            }
        }
    }
    
    /**
     * Tải cấu hình mặc định khi không tìm thấy file config
     */
    private void loadDefaultConfig() {
        properties = new Properties();
        
        // Server configuration
        properties.setProperty("server.port", "8122");
        
        // Database configuration
        properties.setProperty("database.url", "********************************");
        properties.setProperty("database.username", "root");
        properties.setProperty("database.password", "");
        
        // Connection pool settings
        properties.setProperty("database.pool.maximum", "10");
        properties.setProperty("database.pool.minimum", "5");
        properties.setProperty("database.connection.timeout", "30000");
        properties.setProperty("database.max.lifetime", "1800000");
        
        // Game configuration
        properties.setProperty("game.auto.generate.bot", "true");
        properties.setProperty("game.max.bot.count", "50");
        
        System.out.println("⚙️ Đã tải cấu hình mặc định");
    }
    
    /**
     * Lấy giá trị cấu hình dạng String
     */
    public String getString(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * Lấy giá trị cấu hình dạng String với giá trị mặc định
     */
    public String getString(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * Lấy giá trị cấu hình dạng int
     */
    public int getInt(String key) {
        String value = properties.getProperty(key);
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            System.err.println("❌ Lỗi định dạng số cho key: " + key + " = " + value);
            return 0;
        }
    }
    
    /**
     * Lấy giá trị cấu hình dạng int với giá trị mặc định
     */
    public int getInt(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            System.err.println("❌ Lỗi định dạng số cho key: " + key + " = " + value + ", sử dụng giá trị mặc định: " + defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * Lấy giá trị cấu hình dạng boolean
     */
    public boolean getBoolean(String key) {
        String value = properties.getProperty(key);
        return Boolean.parseBoolean(value);
    }
    
    /**
     * Lấy giá trị cấu hình dạng boolean với giá trị mặc định
     */
    public boolean getBoolean(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }
    
    /**
     * Lấy giá trị cấu hình dạng long
     */
    public long getLong(String key) {
        String value = properties.getProperty(key);
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            System.err.println("❌ Lỗi định dạng số cho key: " + key + " = " + value);
            return 0L;
        }
    }
    
    /**
     * Lấy giá trị cấu hình dạng long với giá trị mặc định
     */
    public long getLong(String key, long defaultValue) {
        String value = properties.getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            System.err.println("❌ Lỗi định dạng số cho key: " + key + " = " + value + ", sử dụng giá trị mặc định: " + defaultValue);
            return defaultValue;
        }
    }
    
    // ==========================================
    // HELPER METHODS FOR SPECIFIC CONFIGURATIONS
    // ==========================================
    
    /**
     * Lấy cổng server
     */
    public int getServerPort() {
        return getInt("server.port", 8122);
    }
    
    /**
     * Lấy URL database
     */
    public String getDatabaseUrl() {
        return getString("database.url", "********************************");
    }
    
    /**
     * Lấy username database
     */
    public String getDatabaseUsername() {
        return getString("database.username", "root");
    }
    
    /**
     * Lấy password database
     */
    public String getDatabasePassword() {
        return getString("database.password", "");
    }
    
    /**
     * Lấy số kết nối tối đa trong pool
     */
    public int getDatabasePoolMaximum() {
        return getInt("database.pool.maximum", 10);
    }
    
    /**
     * Lấy số kết nối tối thiểu trong pool
     */
    public int getDatabasePoolMinimum() {
        return getInt("database.pool.minimum", 5);
    }
    
    /**
     * Lấy thời gian chờ kết nối
     */
    public long getDatabaseConnectionTimeout() {
        return getLong("database.connection.timeout", 30000L);
    }
    
    /**
     * Lấy thời gian sống tối đa của kết nối
     */
    public long getDatabaseMaxLifetime() {
        return getLong("database.max.lifetime", 1800000L);
    }
    
    /**
     * Kiểm tra có tự động tạo bot hay không
     */
    public boolean isAutoGenerateBot() {
        return getBoolean("game.auto.generate.bot", true);
    }
    
    /**
     * Lấy số lượng bot tối đa
     */
    public int getMaxBotCount() {
        return getInt("game.max.bot.count", 50);
    }
    
    /**
     * Cache all configuration values for performance
     */
    private void cacheAllConfiguration() {
        // Server configuration
        cachedServerPort = getInt("server.port", 8122);
        
        // Database configuration
        cachedDatabaseUrl = getString("database.url", "********************************");
        cachedDatabaseUsername = getString("database.username", "root");
        cachedDatabasePassword = getString("database.password", "");
        cachedDatabasePoolMaximum = getInt("database.pool.maximum", 10);
        cachedDatabasePoolMinimum = getInt("database.pool.minimum", 5);
        cachedDatabaseConnectionTimeout = getLong("database.connection.timeout", 30000L);
        cachedDatabaseMaxLifetime = getLong("database.max.lifetime", 1800000L);
        
        // Game configuration
        cachedAutoGenerateBot = getBoolean("game.auto.generate.bot", true);
        cachedMaxBotCount = getInt("game.max.bot.count", 50);
        
        // Resource versions
        cachedIconVersion = getInt("resource.icon.version", 12);
        cachedMapVersion = getInt("resource.map.version", 11);
        cachedLayerVersion = getInt("resource.layer.version", 12);
        cachedDataVersion = getInt("resource.data.version", 12);
        cachedCaptionVersion = getInt("resource.caption.version", 12);
    }
    
    /**
     * Get cached configuration values (instance methods with cached values)
     */
    public int getServerPortCached() {
        return cachedServerPort;
    }
    
    public String getDatabaseUrlCached() {
        return cachedDatabaseUrl;
    }
    
    public String getDatabaseUsernameCached() {
        return cachedDatabaseUsername;
    }
    
    public String getDatabasePasswordCached() {
        return cachedDatabasePassword;
    }
    
    public int getDatabasePoolMaximumCached() {
        return cachedDatabasePoolMaximum;
    }
    
    public int getDatabasePoolMinimumCached() {
        return cachedDatabasePoolMinimum;
    }
    
    public long getDatabaseConnectionTimeoutCached() {
        return cachedDatabaseConnectionTimeout;
    }
    
    public long getDatabaseMaxLifetimeCached() {
        return cachedDatabaseMaxLifetime;
    }
    
    public boolean isAutoGenerateBotCached() {
        return cachedAutoGenerateBot;
    }
    
    public int getMaxBotCountCached() {
        return cachedMaxBotCount;
    }
    
    // Resource versions
    public int getIconVersionCached() {
        return cachedIconVersion;
    }
    
    public int getMapVersionCached() {
        return cachedMapVersion;
    }
    
    public int getLayerVersionCached() {
        return cachedLayerVersion;
    }
    
    public int getDataVersionCached() {
        return cachedDataVersion;
    }
    
    public int getCaptionVersionCached() {
        return cachedCaptionVersion;
    }
    
    /**
     * In ra tất cả cấu hình hiện tại
     */
    public void printAllConfig() {
        System.out.println("==========================================");
        System.out.println("           CẤU HÌNH SERVER (CACHED)");
        System.out.println("==========================================");
        System.out.println("Server Port: " + cachedServerPort);
        System.out.println("Database URL: " + cachedDatabaseUrl);
        System.out.println("Database Username: " + cachedDatabaseUsername);
        System.out.println("Database Password: " + (cachedDatabasePassword.isEmpty() ? "(trống)" : "***"));
        System.out.println("Pool Maximum: " + cachedDatabasePoolMaximum);
        System.out.println("Pool Minimum: " + cachedDatabasePoolMinimum);
        System.out.println("Connection Timeout: " + cachedDatabaseConnectionTimeout + "ms");
        System.out.println("Max Lifetime: " + cachedDatabaseMaxLifetime + "ms");
        System.out.println("Auto Generate Bot: " + cachedAutoGenerateBot);
        System.out.println("Max Bot Count: " + cachedMaxBotCount);
        System.out.println("Resource Versions - Icon: " + cachedIconVersion + ", Map: " + cachedMapVersion + ", Layer: " + cachedLayerVersion + ", Data: " + cachedDataVersion + ", Caption: " + cachedCaptionVersion);
        System.out.println("==========================================");
    }
}
