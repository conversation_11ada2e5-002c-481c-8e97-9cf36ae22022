package mobiarmy.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;

public class Pet {
    public static Pet[] entrys;

    public int id;
    public String name;
    public int itemId;
    public int frameMoveStart;
    public int frameMoveEnd;
    public int frameAttackStart;
    public int frameAttackEnd;
    public int dxMove;
    public int dyMove;
    public int dxAtk;
    public int dyAtk;
    public byte tickMove;
    public byte tickAtk;
    
    /**
     * <PERSON>ad tất cả pets từ database
     */
    public static void loadPet() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM pet ORDER BY id");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Pet> tempList = new ArrayList<>();
            while (rs.next()) {
                Pet pet = new Pet();
                pet.id = rs.getInt("id");
                pet.name = rs.getString("name");
                pet.itemId = rs.getInt("itemid");
                pet.frameMoveStart = rs.getInt("frameMoveStart");
                pet.frameMoveEnd = rs.getInt("frameMoveEnd");
                pet.frameAttackStart = rs.getInt("frameAttackStart");
                pet.frameAttackEnd = rs.getInt("frameAttackEnd");
                pet.dxMove = rs.getInt("dxMove");
                pet.dyMove = rs.getInt("dyMove");
                pet.dxAtk = rs.getInt("dxAtk");
                pet.dyAtk = rs.getInt("dyAtk");
                pet.tickMove = rs.getByte("tickMove");
                pet.tickAtk = rs.getByte("tickAtk");
                tempList.add(pet);
            }
            entrys = tempList.toArray(new Pet[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " pets từ database");
    }
    public static Pet get(int itemId) {
        for (Pet pet : entrys) {
            if (pet.itemId == itemId) {
                return pet;
            }
        }
        return null;
    }
}
