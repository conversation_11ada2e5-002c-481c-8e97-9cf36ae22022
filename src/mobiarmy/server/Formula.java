package mobiarmy.server;

import org.json.simple.JSONArray;
import org.json.simple.JSONValue;

import mobiarmy.db.DBManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Random;

/**
 * Class quản lý công thức chế tạo (Formula)
 * <AUTHOR>
 */
public class Formula {
    
    public byte id;
    public byte idMaterial;
    public byte lv;
    public String name;
    public short lvRequire;
    public byte equipType;
    public short[] equipId;
    public short[] equipNeed;
    public short[] addPNMin;
    public short[] addPNMax;
    public short[] addPP100Min;
    public short[] addPP100Max;
    public MaterialRequirement[] itemRequire;
    public String[] detail;
    
    public static Formula[] entrys;
    private static Random random = new Random();
    
    public Formula() {}
    
    /**
     * Class để lưu thông tin nguyên liệu cần thiết
     */
    public static class MaterialRequirement {
        public byte id;
        public int num;
        
        public MaterialRequirement(byte id, int num) {
            this.id = id;
            this.num = num;
        }
    }
    
    /**
     * Load tất cả formula từ database
     */
    public static void loadFormula() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM fomular ORDER BY id");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Formula> tempList = new ArrayList<>();
            
            while (rs.next()) {
                Formula formula = new Formula();
                formula.id = rs.getByte("id");
                formula.idMaterial = rs.getByte("idMaterial");
                formula.lv = rs.getByte("lv");
                formula.name = rs.getString("name");
                formula.lvRequire = rs.getShort("lvRequire");
                formula.equipType = rs.getByte("equipType");
                // Parse equipId array
                formula.equipId = parseShortArray(rs.getString("equipId"));
                formula.equipNeed = parseShortArray(rs.getString("equipNeed"));
                formula.addPNMin = parseShortArray(rs.getString("addPNMin"));
                formula.addPNMax = parseShortArray(rs.getString("addPNMax"));
                formula.addPP100Min = parseShortArray(rs.getString("addPP100Min"));
                formula.addPP100Max = parseShortArray(rs.getString("addPP100Max"));
                formula.detail = parseStringArray(rs.getString("detail"));
                
                // Parse itemRequire JSON
                formula.parseItemRequire(rs.getString("itemRequire"));
                
                tempList.add(formula);
            }
            
            entrys = tempList.toArray(new Formula[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " formulas từ database");
    }
    
    /**
     * Parse chuỗi JSON itemRequire thành array MaterialRequirement
     */
    private void parseItemRequire(String itemRequireJson) {
        try {
            // Remove brackets and split by },{ pattern
            String cleaned = itemRequireJson.replace("[", "").replace("]", "");
            if (cleaned.trim().isEmpty()) {
                this.itemRequire = new MaterialRequirement[0];
                return;
            }
            
            String[] items = cleaned.split("\\},\\{");
            this.itemRequire = new MaterialRequirement[items.length];
            
            for (int i = 0; i < items.length; i++) {
                String item = items[i].replace("{", "").replace("}", "");
                String[] parts = item.split(",");
                
                byte id = 0;
                int num = 0;
                
                for (String part : parts) {
                    String[] keyValue = part.split(":");
                    if (keyValue.length == 2) {
                        String key = keyValue[0].replace("\"", "").trim();
                        String value = keyValue[1].replace("\"", "").trim();
                        
                        if (key.equals("id")) {
                            id = Byte.parseByte(value);
                        } else if (key.equals("num")) {
                            num = Integer.parseInt(value);
                        }
                    }
                }
                
                this.itemRequire[i] = new MaterialRequirement(id, num);
            }
        } catch (Exception e) {
            System.err.println("Error parsing itemRequire: " + itemRequireJson + " - " + e.getMessage());
            this.itemRequire = new MaterialRequirement[0];
        }
    }
    
    /**
     * Lấy formula theo ID
     */
    public static Formula get(int id,int level) {
        for (Formula formula : entrys) {
            if (formula.idMaterial == id && formula.lv == level) {
                return formula;
            }
        }
        return null;
    }
    
    /**
     * Lấy danh sách formula theo idMaterial (loại công thức)
     */
    public static ArrayList<Formula> getByMaterial(int idMaterial) {
        ArrayList<Formula> result = new ArrayList<>();
        for (Formula formula : entrys) {
            if (formula.idMaterial == idMaterial) {
                result.add(formula);
            }
        }
        return result;
    }
    
    /**
     * Random inv_ability từ addPNMin và addPNMax
     * Khi random không được sẽ trả về min, không trả về 0
     */
    public byte[] randomInvAbility() {
        if (addPNMin == null || addPNMax == null || addPNMin.length != addPNMax.length) {
            // Trả về mảng với giá trị min nếu có, không phải 0
            byte[] result = new byte[5];
            if (addPNMin != null && addPNMin.length > 0) {
                for (int i = 0; i < 5; i++) {
                    result[i] = (byte) addPNMin[Math.min(i, addPNMin.length - 1)];
                }
            }
            return result;
        }
        
        byte[] result = new byte[5];
        for (int i = 0; i < Math.min(5, addPNMin.length); i++) {
            if (addPNMax[i] >= addPNMin[i]) {
                result[i] = (byte) (addPNMin[i] + random.nextInt(addPNMax[i] - addPNMin[i] + 1));
            } else {
                result[i] = (byte) addPNMin[i]; // Trả về min khi max < min
            }
        }
        return result;
    }
    
    /**
     * Random inv_percen từ addPP100Min và addPP100Max
     * Khi random không được sẽ trả về min, không trả về 0
     */
    public byte[] randomInvPercen() {
        if (addPP100Min == null || addPP100Max == null || addPP100Min.length != addPP100Max.length) {
            // Trả về mảng với giá trị min nếu có, không phải 0
            byte[] result = new byte[5];
            if (addPP100Min != null && addPP100Min.length > 0) {
                for (int i = 0; i < 5; i++) {
                    result[i] = (byte) addPP100Min[Math.min(i, addPP100Min.length - 1)];
                }
            }
            return result;
        }
        
        byte[] result = new byte[5];
        for (int i = 0; i < Math.min(5, addPP100Min.length); i++) {
            if (addPP100Max[i] >= addPP100Min[i]) {
                result[i] = (byte) (addPP100Min[i] + random.nextInt(addPP100Max[i] - addPP100Min[i] + 1));
            } else {
                result[i] = (byte) addPP100Min[i]; // Trả về min khi max < min
            }
        }
        return result;
    }
    
    /**
     * Kiểm tra user có đủ nguyên liệu không
     */
    public boolean hasRequiredMaterials(User user) {
        if (itemRequire == null) return true;
        
        for (MaterialRequirement requirement : itemRequire) {
            LinhTinh userMaterial = user.getLinhTinh(requirement.id);
            if (userMaterial == null || userMaterial.num < requirement.num) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Kiểm tra user có đủ level yêu cầu không
     */
    public boolean hasRequiredLevel(User user) {
        return user.glass().level >= this.lvRequire;
    }
    
    /**
     * Kiểm tra user có equipment cần thiết không
     */
    public boolean hasRequiredEquipment(User user) {
        if (equipNeed == null || equipNeed.length == 0) return true;
        
        // Lấy equipment ID cần thiết theo glass index
        short requiredEquipId = equipNeed[user.selectGlass];
        Equip equip = user.getEquip(requiredEquipId,equipType);
        if(this.lv > 1){
            requiredEquipId = equipId[user.selectGlass];
            equip = user.getEquip(requiredEquipId,equipType,(byte)(this.lv-1));
        }
        return equip != null;
    }
    
    /**
     * Trừ nguyên liệu từ user khi chế tạo thành công
     */
    public boolean consumeMaterials(User user) {
        if (itemRequire == null) return false;
        
        for (MaterialRequirement requirement : itemRequire) {
            user.addLinhTinh(requirement.id, -requirement.num);
        }
        user.addLinhTinh(idMaterial, -1);

        Equip equip = user.getEquip(equipNeed[user.selectGlass],equipType);
        if(this.lv > 1){
            equip = user.getEquip(equipId[user.selectGlass],equipType,(byte)(this.lv-1));
        }
        if (equip != null) {
            user.removeEquip(equip);
        }else{
            return false;
        }
        return true;

    }
    
    /**
     * Tạo deep copy của formula
     */
    public Formula deepCopy() {
        Formula copy = new Formula();
        copy.id = this.id;
        copy.idMaterial = this.idMaterial;
        copy.lv = this.lv;
        copy.name = this.name;
        copy.lvRequire = this.lvRequire;
        copy.equipType = this.equipType;
        copy.equipId = this.equipId != null ? this.equipId.clone() : null;
        copy.equipNeed = this.equipNeed != null ? this.equipNeed.clone() : null;
        copy.addPNMin = this.addPNMin != null ? this.addPNMin.clone() : null;
        copy.addPNMax = this.addPNMax != null ? this.addPNMax.clone() : null;
        copy.addPP100Min = this.addPP100Min != null ? this.addPP100Min.clone() : null;
        copy.addPP100Max = this.addPP100Max != null ? this.addPP100Max.clone() : null;
        copy.detail = this.detail != null ? this.detail.clone() : null;
        
        if (this.itemRequire != null) {
            copy.itemRequire = new MaterialRequirement[this.itemRequire.length];
            for (int i = 0; i < this.itemRequire.length; i++) {
                copy.itemRequire[i] = new MaterialRequirement(this.itemRequire[i].id, this.itemRequire[i].num);
            }
        }
        
        return copy;
    }
    
    // Helper methods for parsing JSON arrays
    private static short[] parseShortArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        short[] result = new short[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            result[i] = Short.parseShort(jsonArray.get(i).toString());
        }
        return result;
    }
    
    private static String[] parseStringArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        String[] result = new String[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            result[i] = jsonArray.get(i) != null ? jsonArray.get(i).toString() : null;
        }
        return result;
    }
}
