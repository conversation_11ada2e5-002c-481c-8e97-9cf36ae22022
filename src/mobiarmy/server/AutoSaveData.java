package mobiarmy.server;

import mobiarmy.util.Log;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by k<PERSON><PERSON> on 9/20/2025.
 *
 * <AUTHOR>
 */
public class AutoSaveData implements Runnable {

    @Override
    public void run() {
        while (Server.isRunning) {
            try {
                Thread.sleep(360000);
                SessionManager.saveUsers();
                Log.info("Lưu data tự động");
            } catch (Exception ex) {
                Logger.getLogger(AutoSaveData.class.getName()).log(Level.SEVERE, null, ex);
            }

        }
    }

}

