package mobiarmy.server;

import mobiarmy.db.DBManager;
import mobiarmy.server.JsonUtil;
import org.json.simple.JSONArray;
import org.json.simple.JSONValue;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

/**
 *
 * <AUTHOR> Tú
 */
public class Glass {
    
    public byte id;
    public String name;
    public int xu;
    public int luong;
    public int friction;
    public int angle;
    public int att;
    public int distance;
    public int bullet;
    
    public User         user;
    public int[]        ability;
    public short[]      equipID;
    public short[]      data;
    public int[]        dbKey;
    public int          point;
    public int          level;
    public int          exp;
    public boolean      isOpen;
    
    public static Glass[] entrys;
    
    public static void loadGlass() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM glass");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Glass> tempList = new ArrayList<>();
            while (rs.next()) {
                Glass glass = new Glass();
                glass.id = rs.getByte("id");
                glass.name = rs.getString("name");
                // Parse equipID array
                String equipIDJson = rs.getString("equipID");
                if (equipIDJson != null && !equipIDJson.trim().isEmpty() && !equipIDJson.equals("null")) {
                    JSONArray equipArray = (JSONArray) JSONValue.parse(equipIDJson);
                    if (equipArray != null) {
                        glass.equipID = new short[equipArray.size()];
                        for (int j = 0; j < equipArray.size(); j++) {
                            glass.equipID[j] = Short.parseShort(equipArray.get(j).toString());
                        }
                    }
                }
                
                // Parse ability array
                String abilityJson = rs.getString("ability");
                if (abilityJson != null && !abilityJson.trim().isEmpty() && !abilityJson.equals("null")) {
                    JSONArray abilityArray = (JSONArray) JSONValue.parse(abilityJson);
                    if (abilityArray != null) {
                        glass.ability = new int[abilityArray.size()];
                        for (int j = 0; j < abilityArray.size(); j++) {
                            glass.ability[j] = Integer.parseInt(abilityArray.get(j).toString());
                        }
                    }
                }
                glass.att = rs.getInt("att");
                glass.friction = rs.getInt("friction");
                glass.angle = rs.getInt("angle");
                glass.distance = rs.getInt("distance");
                glass.bullet = rs.getInt("bullet");
                glass.xu = rs.getInt("xu");
                glass.luong = rs.getInt("luong");
                tempList.add(glass);
            }
            entrys = new Glass[tempList.size()];
            for (Glass glass : tempList) {
                entrys[glass.id] = glass;
            }
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " glass từ database");
    }
    public Glass() {
        this.dbKey = new int[5];
        this.ability = new int[5];
    }
    
    public void updateAll() {
        this.level = Exp.getLevelExp(this.exp);
        this.equipID = entrys[this.id].equipID.clone();
        this.data = null;
        this.dbKey = new int[]{-1, -1, -1, -1, -1};
        //Trang bị thường
        for (Equip equip : this.user.equips) {
            if (equip.glassID == this.id && equip.isUse) {
                if (equip.date() == 0) {
                    equip.isUse = false;
                } else if (equip.vip != 0) {
                    this.data = equip.data != null ? equip.data.clone() : null;
                } else {
                    this.equipID[equip.type] = equip.id;
                    this.dbKey[equip.type] = equip.dbKey;
                }
            }
        }
    }
    
    public int getPercentLevel() {
        return Exp.getPercentExp(this.level, this.exp);
    }
    
    public int getExpMaxLevel() {
        return Exp.get(this.level).exp;
    }
    
    public void upadtePoint(int[] pointAdd) {
        int totalPoint = 0;
        for (int i = 0; i < pointAdd.length; i++) {
            if (pointAdd[i] < 0) {
                return;
            }
            totalPoint = totalPoint + pointAdd[i];
        }
        if (totalPoint != 0 && totalPoint <= this.point) {
            for (int i = 0; i < pointAdd.length; i++) {
                this.ability[i] = this.ability[i] + pointAdd[i];
            }
            this.updateAll();
            this.point = this.point - totalPoint;
            if (this.user.session != null) {
                this.user.session.sessionHandler.loadInfo();
            }
        }
    }
    
    public void addExp(int add, boolean flag) {
        this.exp = this.exp + add;
        if (this.exp > Exp.entrys[Exp.entrys.length - 1].exp) {
            this.exp = Exp.entrys[Exp.entrys.length - 1].exp;
        }
        int d = Exp.getLevelExp(this.exp) - this.level;
        if (d != 0) {
            this.point = this.point + d * 3;
            this.level = Exp.getLevelExp(this.exp);
            if (flag && this.user != null && this.user.session != null) {
                this.user.session.sessionHandler.updateExp(add, this.exp, this.getExpMaxLevel(), this.level, this.getPercentLevel(), this.point);
            }
        } else {
            if (flag && this.user != null && this.user.session != null) {
                this.user.session.sessionHandler.updateExp(add, this.exp, this.getExpMaxLevel(), this.getPercentLevel());
            }
        }
    }
    
    public int[] createAbility() {
        int iArr[] = new int[5];
        int[] envAdd = new int[5];
        int[] percenAdd = new int[5];
        for (int i = 0; i < envAdd.length; i++) {
            for (Equip equip : this.user.equips) {
                if (equip.glassID == this.id && equip.isUse) {
                    envAdd[i] = envAdd[i] + equip.inv_ability[i];
                    percenAdd[i] = percenAdd[i] + equip.inv_percen[i];
                }
            }
        }
        LinhTinh[] equipPet = this.user.getEquipPet();
        for (int i = 0; i < equipPet.length; i++) {
            if (equipPet[i] != null) {
                for (int j = 0; j < equipPet[i].ability.length; j++) {
                    envAdd[j] = envAdd[j] + equipPet[i].ability[j];
                }
            }
        }
        //HP
        iArr[0] = (1000 + (this.ability[0] * 10)) + (envAdd[0] * 10) + ((1000 + this.ability[0]) * percenAdd[0] / 100);
        //SM
        iArr[1] = this.att * (((this.ability[1] + envAdd[1]) / 3) + 100 + percenAdd[1]) / 100;
        //PT
        iArr[2] = ((this.ability[2] + envAdd[2]) * 10);
        iArr[2] = iArr[2] + (iArr[2] * percenAdd[2] / 100);
        //MM
        iArr[3] = ((this.ability[3] + envAdd[3]) * 10);
        iArr[3] = iArr[3] + (iArr[3] * percenAdd[3] / 100);
        //DD
        iArr[4] = ((this.ability[4] + envAdd[4]) * 10);
        iArr[4] = iArr[4] + (iArr[4] * percenAdd[4] / 100);
        return iArr;
    }

    public Glass deepCopy() {
        Glass glass = new Glass();
        glass.id = this.id;
        glass.name = this.name;
        glass.xu = this.xu;
        glass.luong = this.luong;
        glass.friction = this.friction;
        glass.angle = this.angle;
        glass.att = this.att;
        glass.distance = this.distance;
        glass.bullet = this.bullet;
        glass.point = this.point;
        glass.level = this.level;
        glass.exp = this.exp;
        glass.isOpen = this.isOpen;
        glass.ability = this.ability != null ? this.ability.clone() : null;
        glass.equipID = this.equipID != null ? this.equipID.clone() : null;
        glass.data = this.data != null ? this.data.clone() : null;
        glass.dbKey = this.dbKey != null ? this.dbKey.clone() : null;
        glass.user = this.user;

        return glass;
    }
    
    /**
     * Reset điểm nâng cấp của nhân vật về 0 và hoàn lại điểm dựa trên level hiện tại
     */
    public void resetUpgradePoints() {
        // Reset ability về 0
        for (int i = 0; i < this.ability.length; i++) {
            this.ability[i] = 0;
        }
        // Tính lại điểm dựa trên level hiện tại (3 điểm mỗi level)
        this.point = this.level * 3;
        this.updateAll();
    }

}