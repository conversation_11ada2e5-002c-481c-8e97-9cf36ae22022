package mobiarmy.server;

import mobiarmy.db.DBManager;
import mobiarmy.server.JsonUtil;
import org.json.simple.JSONArray;
import org.json.simple.JSONValue;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;


public class Equip {
    
    public byte glassID;
    public short id;
    public byte type;
    public byte bullet;
    public short icon;
    public byte level;
    public byte level2;
    public short[] x;
    public short[] y;
    public byte[] w;
    public byte[] h;
    public byte[] dx;
    public byte[] dy;
    public byte[] inv_ability;
    public byte[] inv_percen;
    public byte date;
    public int luong;
    public int xu;
    public short[] slot;
    public String name;
    public byte vip;
    public short[] data;
    public int dbKey;
    public boolean isUse;
    public long renewalDate;
    
    public Equip() {
        this.slot = new short[]{-1, -1, -1};
    }
    
    public int slot() {
        int num = this.slot.length;
        for (int i = 0; i < this.slot.length; i++) {
            if (this.slot[i] != -1) {
                num--;
            }
        }
        return num;
    }
    
    public int priceSlot() {
        int sum = 0;
        for (int i = 0; i < this.slot.length; i++) {
            if (this.slot[i] != -1) {
                sum += LinhTinh.get(this.slot[i]).xu;
            }
        }
        return sum;
    }
    
    public int calculatePrice() {
        int sum = 0;
        if (this.xu != -1) {
            sum = this.xu / 2;
        } else if (this.luong != -1) {
            sum = this.luong * 500;
        }
        if (sum > 0) {
            sum = (int) ((float)sum / (float)this.date * (float)this.date());
        }
        return sum;
    }
    
    public int renewalPrice() {
        int sum = (int) (this.priceSlot() * 0.05F);
        if (this.xu != -1) {
            sum += this.xu;
        } else if (this.luong != -1) {
            sum += this.luong * 1000;
        }
        return sum;
    }
    
    public int date() {
        int d = (int) (this.date - ((System.currentTimeMillis() - this.renewalDate) / (1000 * 60 * 60 * 24)));
        if (d < 0) {
            d = 0;
        }
        return d;
    }

    public static Equip[] entrys;
    public static HashMap<Byte, HashMap<Byte, ArrayList<Equip>>> equipsByGlassIDAndType = new HashMap<>();
    
    public static void loadEquip() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM equip");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Equip> tempList = new ArrayList<>();
            equipsByGlassIDAndType.clear();
            
            while (rs.next()) {
                Equip equip = new Equip();
                equip.glassID = rs.getByte("glassID");
                equip.id = rs.getShort("id");
                equip.type = rs.getByte("type");
                equip.bullet = rs.getByte("bullet");
                equip.icon = rs.getShort("icon");
                equip.level = rs.getByte("level");
                equip.x = parseShortArray(rs.getString("x"));
                equip.y = parseShortArray(rs.getString("y"));
                equip.w = parseByteArray(rs.getString("w"));
                equip.h = parseByteArray(rs.getString("h"));
                equip.dx = parseByteArray(rs.getString("dx"));
                equip.dy = parseByteArray(rs.getString("dy"));
                equip.inv_ability = parseByteArray(rs.getString("inv_ability"));
                equip.inv_percen = parseByteArray(rs.getString("inv_percen"));
                equip.date = rs.getByte("date");
                equip.luong = rs.getInt("luong");
                equip.xu = rs.getInt("xu");
                equip.name = rs.getString("name");
                equip.vip = rs.getByte("vip");
                equip.data = parseShortArray(rs.getString("data"));
                
                tempList.add(equip);
                
                equipsByGlassIDAndType
                    .computeIfAbsent(equip.glassID, k -> new HashMap<>())  // Tạo HashMap cho glassID nếu chưa có
                    .computeIfAbsent(equip.type, k -> new ArrayList<>())   // Tạo ArrayList cho type nếu chưa có
                    .add(equip);                                           // Thêm trang bị vào danh sách
            }
            
            entrys = tempList.toArray(new Equip[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " equips từ database");
    }
        
    public static Equip get(byte glassID, short id) {
        for (Equip equip : entrys) {
            if (equip.glassID == glassID && equip.id == id) {
                return equip;
            }
        }
        return null;
    }
        
    public static Equip get(byte glassID, int id, int type) {
        for (Equip equip : entrys) {
            if (equip.glassID == glassID && equip.id == id && equip.type == type) {
                return equip;
            }
        }
        return null;
    }
    
    public Equip deepCopy() {
        Equip copy = new Equip();

        // Copying primitive fields
        copy.glassID = this.glassID;
        copy.id = this.id;
        copy.type = this.type;
        copy.bullet = this.bullet;
        copy.icon = this.icon;
        copy.level = this.level;
        copy.level2 = this.level2;
        copy.date = this.date;
        copy.luong = this.luong;
        copy.xu = this.xu;
        copy.slot = this.slot != null ? this.slot.clone() : null;
        copy.name = this.name; // Assuming String is immutable, so shallow copy is fine
        copy.vip = this.vip;
        copy.dbKey = this.dbKey;
        copy.isUse = this.isUse;

        // Deep copying arrays
        copy.x = this.x != null ? this.x.clone() : null;
        copy.y = this.y != null ? this.y.clone() : null;
        copy.w = this.w != null ? this.w.clone() : null;
        copy.h = this.h != null ? this.h.clone() : null;
        copy.dx = this.dx != null ? this.dx.clone() : null;
        copy.dy = this.dy != null ? this.dy.clone() : null;
        copy.inv_ability = this.inv_ability != null ? this.inv_ability.clone() : null;
        copy.inv_percen = this.inv_percen != null ? this.inv_percen.clone() : null;
        copy.data = this.data != null ? this.data.clone() : null;

        return copy;
    }
    
    // Helper methods for parsing JSON arrays
    private static short[] parseShortArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        short[] result = new short[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            result[i] = Short.parseShort(jsonArray.get(i).toString());
        }
        return result;
    }
    
    private static byte[] parseByteArray(String json) {
        if (json == null || json.trim().isEmpty() || json.equals("null")) {
            return null;
        }
        JSONArray jsonArray = (JSONArray) JSONValue.parse(json);
        if (jsonArray == null) return null;
        
        byte[] result = new byte[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            result[i] = Byte.parseByte(jsonArray.get(i).toString());
        }
        return result;
    }
}
