package mobiarmy.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;

/**
 *
 * <AUTHOR> Tú
 */
public class MapBoss {

    public int mapID;
    public int glassID;
    
    public static MapBoss[] entrys;
    
    public static void loadMapBoss() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM map_boss");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<MapBoss> tempList = new ArrayList<>();
            
            while (rs.next()) {
                MapBoss mapBoss = new MapBoss();
                mapBoss.mapID = rs.getInt("mapID");
                mapBoss.glassID = rs.getInt("glassID");
                tempList.add(mapBoss);
            }
            
            entrys = tempList.toArray(new MapBoss[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " map bosses từ database");
    }
}
