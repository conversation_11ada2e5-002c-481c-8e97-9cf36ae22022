package mobiarmy.server;

import mobiarmy.db.DBManager;
import mobiarmy.server.JsonUtil;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

/**
 *
 * <AUTHOR> Tú
 */
public class Map {

    public byte id;
    public String name;
    public String file;
    public Data data;
    public short bg;
    public short mapAddY;
    public short cl2AddY;
    public short inWaterAddY;
    public short bullEffShower;

    public static class Data {
        public short width;
        public short height;
        public Brick bricks[];
        public Point points[];
    }

    public static class Brick {
        public int id;
        public int x;
        public int y;
    }

    public static class Point {
        public short x;
        public short y;
    }

    public static Map[] entrys;

    public static void loadMap() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM map");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Map> tempList = new ArrayList<>();
            
            while (rs.next()) {
                Map map = new Map();
                map.id = rs.getByte("id");
                map.name = rs.getString("name");
                map.file = rs.getString("file");
                
                // Parse map data JSON
                String dataJson = rs.getString("data");
                if (dataJson != null && !dataJson.trim().isEmpty() && !dataJson.equals("null")) {
                    JSONObject dataObj = (JSONObject) JSONValue.parse(dataJson);
                    if (dataObj != null) {
                        Data data = new Data();
                        data.width = Short.parseShort(dataObj.get("width").toString());
                        data.height = Short.parseShort(dataObj.get("height").toString());

                        // Parse bricks array
                        if (dataObj.get("bricks") != null) {
                            JSONArray bricksArray = (JSONArray) dataObj.get("bricks");
                            data.bricks = new Brick[bricksArray.size()];
                            for (int j = 0; j < bricksArray.size(); j++) {
                                JSONObject brickObj = (JSONObject) bricksArray.get(j);
                                Brick brick = new Brick();
                                brick.id = Integer.parseInt(brickObj.get("id").toString());
                                brick.x = Integer.parseInt(brickObj.get("x").toString());
                                brick.y = Integer.parseInt(brickObj.get("y").toString());
                                data.bricks[j] = brick;
                            }
                        }

                        // Parse points array
                        if (dataObj.get("points") != null) {
                            JSONArray pointsArray = (JSONArray) dataObj.get("points");
                            data.points = new Point[pointsArray.size()];
                            for (int j = 0; j < pointsArray.size(); j++) {
                                JSONObject pointObj = (JSONObject) pointsArray.get(j);
                                Point point = new Point();
                                point.x = Short.parseShort(pointObj.get("x").toString());
                                point.y = Short.parseShort(pointObj.get("y").toString());
                                data.points[j] = point;
                            }
                        }

                        map.data = data;
                    }
                }
                map.bg = rs.getShort("bg");
                map.mapAddY = rs.getShort("mapAddY");
                map.bullEffShower = rs.getShort("bullEffShower");
                map.inWaterAddY = rs.getShort("inWaterAddY");
                map.cl2AddY = rs.getShort("cl2AddY");
                
                tempList.add(map);
            }
            
            entrys = tempList.toArray(new Map[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " maps từ database");
    }

    

    public static Map get(int mapID) {
        for (Map entry : entrys) {
            if (entry.id == mapID) {
                return entry;
            }
        }
        return null;
    }
}
