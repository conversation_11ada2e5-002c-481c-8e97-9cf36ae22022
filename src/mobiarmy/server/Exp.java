package mobiarmy.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;

/**
 *
 * <AUTHOR> Tú
 */
public class Exp {

    private int level;
    public int exp;
    
    public Exp(int level, int exp) {
        this.level = level;
        this.exp = exp;
    }
    
    public static Exp[] entrys;
    
    public static void loadExp() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM exp");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Exp> tempList = new ArrayList<>();
            while (rs.next()) {
                Exp exp = new Exp(rs.getInt("level"), rs.getInt("exp"));
                tempList.add(exp);
            }
            entrys = tempList.toArray(new Exp[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " exp levels từ database");
    }
    
    public static int getLevelExp(int exp) {
        for (Exp entry : entrys) {
            if (entry.exp > exp) {
                return entry.level;
            }
        }
        return entrys[entrys.length - 1].level;
    }
    
    public static Exp get(int level) {
        for (Exp entry : entrys) {
            if (entry.level == level) return entry;
        }
        return null;
    }
    
    public static int getPercentExp(int level, int exp) {
        return (int) (100f / (float)get(level).exp * (float)exp);
    }
}
