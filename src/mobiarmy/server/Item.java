package mobiarmy.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;

/**
 *
 * <AUTHOR> Tú
 */
public class Item {
    
    public int id;
    public String name;
    public int xu;
    public int luong;
    public int carryable;
    public int num;
    
    public static Item[] entrys;
    
    public static void loadItem() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM item");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Item> tempList = new ArrayList<>();
            while (rs.next()) {
                Item o = new Item();
                o.id = rs.getInt("id");
                o.name = rs.getString("name");
                o.xu = rs.getInt("xu");
                o.luong = rs.getInt("luong");
                o.carryable = rs.getInt("carryable");
                tempList.add(o);
            }
            entrys = tempList.toArray(new Item[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
    }
    
    public static Item get(int id) {
        for (Item entry : entrys) {
            if (entry.id == id) return entry;
        }
        return null;
    }
    public Item deepCopy() {
        Item copy = new Item();
        copy.id = this.id;
        copy.name = this.name;
        copy.xu = this.xu;
        copy.luong = this.luong;
        copy.carryable = this.carryable;
        copy.num = this.num;
        return copy;
    }
}
