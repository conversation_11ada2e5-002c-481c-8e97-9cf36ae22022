package mobiarmy.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;

/**
 *
 * <AUTHOR>
 */
public class Mission {

    public int id;
    public int level;
    public String name;
    public int require;
    public int have;
    public String reward;
    public int xu;
    public int luong;
    public int exp;
    public int cup;
    
    public boolean isComplete;
    public boolean isGetReward;
    
    public static Mission entrys[];
    
    public static void loadMission() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM mission");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Mission> tempList = new ArrayList<>();
            
            while (rs.next()) {
                Mission mission = new Mission();
                mission.id = rs.getByte("id");
                mission.level = rs.getByte("level");
                mission.name = rs.getString("name");
                mission.require = rs.getInt("require");
                mission.reward = rs.getString("reward");
                mission.xu = rs.getInt("xu");
                mission.luong = rs.getInt("luong");
                mission.exp = rs.getInt("exp");
                mission.cup = rs.getInt("cup");
                tempList.add(mission);
            }
            
            entrys = tempList.toArray(new Mission[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " missions từ database");
    }
    
    public static Mission get(int id, int level) {
        for (Mission mission : entrys) {
            if (mission.id == id && (level == -1 || mission.level == level)) {
                return mission;
            }
        }
        return null;
    }
    
    public Mission deepCopy() {
        Mission copy = new Mission();
        copy.id = this.id;
        copy.level = this.level;
        copy.name = this.name;
        copy.require = this.require;
        copy.have = this.have;
        copy.reward = this.reward;
        copy.xu = this.xu;
        copy.luong = this.luong;
        copy.exp = this.exp;
        copy.cup = this.cup;
        
        
        copy.isComplete = this.isComplete;
        copy.isGetReward = this.isGetReward;
        return copy;
    }
    
}
