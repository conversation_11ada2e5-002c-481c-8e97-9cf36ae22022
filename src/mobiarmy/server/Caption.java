package mobiarmy.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

import mobiarmy.db.DBManager;

/**
 *
 * <AUTHOR> Tú
 */
public class Caption {
    
    public int level;
    public String caption;
    
    public static Caption[] entrys;
    
    public static void loadCaption() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM caption");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<Caption> tempList = new ArrayList<>();
            while (rs.next()) {
                Caption caption = new Caption();
                caption.caption = rs.getString("caption");
                caption.level = rs.getInt("level");
                tempList.add(caption);
            }
            entrys = tempList.toArray(new Caption[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
        System.out.println("✅ Đã load " + entrys.length + " captions từ database");
    }
    
}
