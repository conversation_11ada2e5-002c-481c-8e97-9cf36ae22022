package mobiarmy.server;

import org.json.simple.JSONArray;
import org.json.simple.JSONValue;

import mobiarmy.db.DBManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

/**
 *
 * <AUTHOR> Tú
 */
public class LinhTinh {

    public int id;
    public String name;
    public String detail;
    public short[] ability;
    public int xu;
    public int luong;
    public int num;
    public byte date;
    public boolean isSelectNum;
    public byte index;
    public boolean isUse;
    public long expire;
    public int indexUI;

    // pet
    public int level = 1;
    public int exp = 1;
    public int expMax = 100;
    public int[] skill = new int[] { -1, -1, -1, -1, -1 };

    public static int[] petNor = { 88, 89, 90 };
    public static int[] petMid = { 91, 92, 93 };
    public static int[] petHigh = { 94, 95, 96 };

    public LinhTinh() {
    };

    public static LinhTinh[] entrys;

    private static boolean contains(int[] array, int value) {
        for (int element : array) {
            if (element == value) {
                return true;
            }
        }
        return false;
    }

    public static void loadLinhTinh() throws SQLException {
        Connection connection = DBManager.getInstance().getConnection(DBManager.SERVER);
        PreparedStatement statement = connection.prepareStatement("SELECT * FROM linhtinh");
        ResultSet rs = statement.executeQuery();
        try {
            ArrayList<LinhTinh> tempList = new ArrayList<>();
            while (rs.next()) {
                LinhTinh linhTinh = new LinhTinh();
                linhTinh.id = rs.getInt("id");
                linhTinh.name = rs.getString("name");
                linhTinh.detail = rs.getString("detail");
                // Parse ability array
                String abilityJson = rs.getString("ability");
                if (abilityJson != null && !abilityJson.trim().isEmpty() && !abilityJson.equals("null")) {
                    JSONArray abilityArray = (JSONArray) JSONValue.parse(abilityJson);
                    if (abilityArray != null) {
                        linhTinh.ability = new short[abilityArray.size()];
                        for (int j = 0; j < abilityArray.size(); j++) {
                            linhTinh.ability[j] = Short.parseShort(abilityArray.get(j).toString());
                        }
                    }
                }
                linhTinh.xu = rs.getInt("xu");
                linhTinh.luong = rs.getInt("luong");
                linhTinh.date = rs.getByte("date");
                linhTinh.index = rs.getByte("index");
                tempList.add(linhTinh);
            }
        
            entrys = tempList.toArray(new LinhTinh[tempList.size()]);
        } finally {
            connection.close();
            rs.close();
            statement.close();
        }
    }

    public static LinhTinh get(int id) {
        for (LinhTinh entry : entrys) {
            if (entry.id == id)
                return entry;
        }
        return null;
    }

    public LinhTinh deepCopy() {
        LinhTinh copy = new LinhTinh();
        copy.id = this.id;
        copy.name = this.name;
        copy.detail = this.detail;
        copy.ability = this.ability.clone();
        copy.xu = this.xu;
        copy.luong = this.luong;
        copy.num = this.num;
        copy.date = this.date;
        copy.isSelectNum = this.isSelectNum;
        copy.index = this.index;
        return copy;
    }

    public float getPercent() {
        return (float) this.exp / (float) this.expMax * 100;
    }

    public boolean addExp(int exp) {
        this.exp = this.exp + exp;
        if (this.exp > this.expMax) {
            this.exp = this.expMax;
            return false;
        }
        return true;
    }

    public boolean isCanLevelUp() {
        return this.exp >= this.expMax;
    }

    public void levelUp() {
        this.level = this.level + 1;
        this.exp = 0;
        this.expMax = this.level * 1000;
        if (contains(petNor, this.id)) {
            int maxIndex = 0;
            for (int i = 1; i < this.ability.length; i++) {
                if (this.ability[i] > this.ability[maxIndex]) {
                    maxIndex = i;
                }
            }
            this.ability[maxIndex]++;
        } else if (contains(petMid, this.id)) {
            int maxIndex = 0;
            for (int i = 1; i < this.ability.length; i++) {
                if (this.ability[i] > this.ability[maxIndex]) {
                    maxIndex = i;
                }
            }
            this.ability[maxIndex] += 2;
        } else if (contains(petHigh, this.id)) {
            int maxIndex = 0;
            for (int i = 1; i < this.ability.length; i++) {
                if (this.ability[i] > this.ability[maxIndex]) {
                    maxIndex = i;
                }
            }
            this.ability[maxIndex] += 3;
        }
    }

    public void upgrade() {
        if (contains(petNor, this.id)) {
            int maxIndex = 0;
            for (int i = 1; i < this.ability.length; i++) {
                if (this.ability[i] > this.ability[maxIndex]) {
                    maxIndex = i;
                }
            }
            this.ability[maxIndex] += 8;
            for (int i = 0; i < this.ability.length; i++) {
                if (i != maxIndex) {
                    this.ability[i] += 4;
                }
            }
        } else if (contains(petMid, this.id)) {
            int maxIndex = 0;
            for (int i = 1; i < this.ability.length; i++) {
                if (this.ability[i] > this.ability[maxIndex]) {
                    maxIndex = i;
                }
            }
            this.ability[maxIndex] += 15;
            for (int i = 0; i < this.ability.length; i++) {
                if (i != maxIndex) {
                    this.ability[i] += 8;
                }
            }
        } else if (contains(petHigh, this.id)) {
            int maxIndex = 0;
            for (int i = 1; i < this.ability.length; i++) {
                if (this.ability[i] > this.ability[maxIndex]) {
                    maxIndex = i;
                }
            }
            this.ability[maxIndex] += 20;
            for (int i = 0; i < this.ability.length; i++) {
                if (i != maxIndex) {
                    this.ability[i] += 10;
                }
            }
        }
    }

    public boolean isCanUpgrade() {
        return isCanLevelUp() && (level == 9 || level == 19);
    }

    public boolean isHaveSkill(int skillId) {
        for (int i = 0; i < this.skill.length; i++) {
            if (this.skill[i] == skillId) {
                return true;
            }
        }
        return false;
    }

    public void addSkill(int skillId) {
        for (int i = 0; i < this.skill.length; i++) {
            if (this.skill[i] == -1) {
                this.skill[i] = skillId;
                return;
            }
        }
    }

    public static int getSkillByLinhTinh(int id) {
        switch (id) {
            case 94:
                return 0;
            case 101:
                return 1;
            case 108:
                return 2;
            case 115:
                return 3;
            case 119:
                return 4;
            case 123:
                return 5;
            case 127:
                return 6;
            case 131:
                return 7;
        }
        return -1;
    }

    public static int getItemSkillByLinhTinh(int id) {
        switch (id) {
            case 88:
            case 89:
            case 90:
            case 91:
            case 92:
            case 93:
                return 94;
            case 95:
            case 96:
            case 97:
            case 98:
            case 99:
            case 100:
                return 101;
            case 102:
            case 103:
            case 104:
            case 105:
            case 106:
            case 107:
                return 108;
            case 109:
            case 110:
            case 111:
            case 112:
            case 113:
            case 114:
                return 115;
            case 116:
            case 117:
            case 118:
                return 119;
            case 120:
            case 121:
            case 122:
                return 123;
            case 124:
            case 125:
            case 126:
                return 127;
            case 128:
            case 129:
            case 130:
                return 131;
        }
        return -1;
    }

    public boolean canAttack() {
        for (int i = 0; i < this.skill.length; i++) {
            if (this.skill[i] != -1) {
                return true;
            }
        }
        return false;
    }

}
