package mobiarmy.server;

import java.util.ArrayList;
import static mobiarmy.server.Text.__;

/**
 *
 * <AUTHOR> <PERSON>ú
 */
public class Select {

    public User user;
    public ArrayList<Element> elements;

    public Select(User user) {
        this.user = user;
        this.elements = new ArrayList<>();
    }

    public void reset() {
        this.elements.clear();
    }

    public void addElement(int type, int id, int quntity) {
        this.elements.add(new Element(type, id, quntity));
    }

    public int sizeOfType(int type) {
        int size = 0;
        for (Element element : this.elements) {
            if (element.type == type) {
                size++;
            }
        }
        return size;
    }

    public Element getFirstOfType(int type) {
        for (Element element : this.elements) {
            if (element.type == type) {
                return element;
            }
        }
        return null;
    }

    public class Element {

        public int type;
        public int id;
        public int num;

        public Element(int type, int id, int num) {
            this.type = type;
            this.id = id;
            this.num = num;
        }
    }

    public void make() {
        if (this.sizeOfType(0) == 1 && this.sizeOfType(1) == 1 && this.getFirstOfType(1).id < 50) {
            this.user.getConfirm().addConfirm(0, __("Bạn có muốn gắn ngọc vào trang bị?."),
                    new int[] { this.getFirstOfType(0).id, this.getFirstOfType(1).id });
        } else if (this.sizeOfType(0) == 0 && this.sizeOfType(1) == 1 && this.getFirstOfType(1).num > 0) {
            Element element = getFirstOfType(1);
            LinhTinh item = this.user.getLinhTinh(this.getFirstOfType(1).id);
            if (item != null) {
                if (element.num > item.num) {
                    element.num = item.num;
                }

                // Xử lý cho các linh tinh có id < 50 (ngọc)
                if (item.id < 50) {
                    if (element.num % 5 == 0 && (item.id + 1) % 10 != 0) {
                        this.user.getConfirm().addConfirm(4, String.format(
                                __("Bạn có muốn kết hợp %d ngọc này thành %d viên ngọc cao cấp hơn? Tỉ lệ thành công là %d%%"),
                                element.num,
                                element.num / 5,
                                100 - ((item.id + 1) % 10) * 10), new int[] { item.id, element.num });
                    } else {
                        this.user.getConfirm().addConfirm(3, String.format(
                                __("Bạn có muốn bán %d vật phẩm này với giá %d xu không?"),
                                element.num, item.xu / 2 * element.num), new int[] { item.id, element.num });
                    }
                }
                // Xử lý cho các linh tinh có id >= 50 (vật phẩm đặc biệt)
                else {
                    handleSpecialItems(item, element);
                }
            }
        } else {
            if (this.user.session != null) {
                this.user.session.sessionHandler.log("Không thể kết hợp.");
            }
        }
    }

    /**
     * Xử lý các vật phẩm đặc biệt có id >= 50
     * 
     * @param item    Vật phẩm linh tinh
     * @param element Element được chọn
     */
    private void handleSpecialItems(LinhTinh item, Element element) {
        switch (item.id) {
            case 50: // Phục hồi điểm nâng cấp
                this.user.getConfirm().addConfirm(5, String.format(
                        __("Bạn có muốn sử dụng %d %s không? Điều này sẽ reset điểm nâng cấp của nhân vật."),
                        element.num, item.name), new int[] { item.id, element.num });
                break;

            case 54: // Kinh nghiệm x2
                this.user.getConfirm().addConfirm(6, String.format(
                        __("Bạn có muốn sử dụng %d %s không? Điều này sẽ tăng gấp đôi kinh nghiệm trong %d ngày."),
                        element.num, item.name, 1 * element.num), new int[] { item.id, element.num });
                break;

            // Công thức chế tạo (id 57-73)
            case 57:
            case 58:
            case 59:
            case 60:
            case 61: // Công thức bạc
            case 69:
            case 70:
            case 71:
            case 72:
            case 73: // Công thức vàng
                this.user.getConfirm().addConfirm(7, String.format(
                        __("Bạn có muốn học %d %s không? Điều này sẽ mở khóa khả năng chế tạo."),
                        element.num, item.name), new int[] { item.id, element.num });
                break;

            // Nguyên liệu chế tạo (id 62-68)
            case 62:
            case 63:
            case 64:
            case 65:
            case 66:
            case 67:
            case 68:
                // Cho phép bán nguyên liệu hoặc sử dụng để chế tạo
                this.user.getConfirm().addConfirm(8, String.format(
                        __("Bạn có muốn bán %d %s với giá %d xu không? (Hoặc giữ lại để chế tạo)"),
                        element.num, item.name, item.xu / 2 * element.num), new int[] { item.id, element.num });
                break;
                case 94:
                case 101:
                case 108:
                case 115:
                case 123:
                case 127:
                case 131:
                    this.user.getConfirm().addConfirm(12, String.format(
                            __("Bạn có muốn học %d %s cho thú cưng không?"),
                            element.num, item.name), new int[] { item.id, element.num });
                    break;
            case 150:
                this.user.getConfirm().addConfirm(11, String.format(
                        __("Bạn có muốn sử dụng %d %s không? Điều này sẽ tăng kinh nghiệm cho thú cưng."),
                        element.num, item.name), new int[] { item.id, element.num });
                break;
                
            default:
                // Các vật phẩm đặc biệt khác - chỉ cho phép bán
                if (item.xu > 0) {
                    this.user.getConfirm().addConfirm(9, String.format(
                            __("Bạn có muốn bán %d %s với giá %d xu không?"),
                            element.num, item.name, item.xu / 2 * element.num), new int[] { item.id, element.num });
                } else {
                    // Vật phẩm không thể bán
                    this.user.getConfirm().addConfirm(10, String.format(
                            __("Bạn có muốn sử dụng %d %s không?"),
                            element.num, item.name), new int[] { item.id, element.num });
                }
                break;
        }
    }
}
