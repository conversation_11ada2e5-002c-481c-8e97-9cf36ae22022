package mobiarmy.server;

import mobiarmy.util.Util;
import mobiarmy.skill.Skill;

import static mobiarmy.server.Text.__;

/**
 *
 * <AUTHOR> Tú
 */
public class Confirm {

    public User user;
    public int action;
    public String str;
    public Object object;

    public Confirm(User user) {
        this.user = user;
        this.action = -1;
    }

    public void addConfirm(int action, String str, Object object) {
        this.action = action;
        this.str = str;
        this.object = object;
        if (this.user.session != null) {
            this.user.session.sessionHandler.confirm(str);
        }
    }

    public void addConfirm2(int action, String str, Object object) {
        this.action = action;
        this.str = str;
        this.object = object;
        this.user.session.sessionHandler.confirm2(str);
    }

    public void confirm() {
        switch (this.action) {
            case 0 -> {
                int array[] = (int[]) this.object;
                Equip equip = this.user.getEquip(array[0]);
                LinhTinh item = this.user.getLinhTinh(array[1]);
                if (equip != null && item != null) {
                    if (equip.slot() > 0) {
                        equip.slot[equip.slot() - 1] = (short) item.id;
                        for (int i = 0; i < item.ability.length; i++) {
                            equip.inv_ability[i] += item.ability[i];
                        }
                        if (this.user.session != null) {
                            this.user.session.sessionHandler.updateEquip(equip);
                        }
                        this.user.addLinhTinh(item.id, -1);
                        if (this.user.session != null) {
                            this.user.session.sessionHandler.log(__("Chúc mừng bạn đã kết hợp thành công."));
                        }
                    } else {
                        if (this.user.session != null) {
                            this.user.session.sessionHandler.log(__("Trang bị đã hết số lần kết hợp."));
                        }
                    }
                }
            }
            case 1 -> {
                int dbKey = (int) this.object;
                Equip equip = this.user.getEquip(dbKey);
                if (equip != null && equip.slot.length - equip.slot() > 0) {
                    int price = (int) (equip.priceSlot() * 0.25);
                    if (price <= this.user.xu) {
                        this.user.addXu(-price, true);
                        for (int i = 0; i < equip.slot.length; i++) {
                            if (equip.slot[i] != -1) {
                                LinhTinh item = LinhTinh.get(equip.slot[i]);
                                if (item != null) {
                                    this.user.addLinhTinh(item.id, 1);
                                    for (int j = 0; j < item.ability.length; j++) {
                                        equip.inv_ability[j] -= item.ability[j];
                                    }
                                }
                            }
                            equip.slot[i] = -1;
                        }
                        if (this.user.session != null) {
                            this.user.session.sessionHandler.updateEquip(equip);
                            this.user.session.sessionHandler.log(__("Tháo lấy ngọc thành công."));
                        }
                    } else {
                        if (this.user.session != null) {
                            this.user.session.sessionHandler.log(__("Bạn không đủ xu để tháo."));
                        }
                    }
                }
            }
            case 2 -> {
                int[] dbKey = (int[]) this.object;
                int price = 0;
                for (int i = 0; i < dbKey.length; i++) {
                    Equip equip = this.user.getEquip(dbKey[i]);
                    if (equip != null && !equip.isUse) {
                        price += equip.calculatePrice();
                        this.user.removeEquip(equip);
                    }
                }
                this.user.addXu(price, true);
                if (this.user.session != null) {
                    this.user.session.sessionHandler.log(String.format(__("Bán thành công, thu được %d xu."), price));
                }
            }
            case 3 -> {
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null) {
                    if (array[1] > item.num) {
                        array[1] = item.num;
                    }
                    this.user.addLinhTinh(item.id, -array[1]);
                    this.user.addXu(item.xu / 2 * array[1], true);
                    if (this.user.session != null) {
                        this.user.session.sessionHandler.log(__("Giao dịch thành công. Xin cảm ơn."));
                    }
                }
            }
            case 4 -> {
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null) {
                    if (array[1] > item.num) {
                        array[1] = item.num / 5;
                    } else {
                        array[1] /= 5;
                    }
                    int count = 0;
                    for (int i = 0; i < array[1]; i++) {
                        boolean flag = Util.nextInt(1, 100) <= 100 - ((item.id + 1) % 10) * 10;
                        if (flag) {
                            count++;
                            this.user.addLinhTinh(item.id, -5);
                            this.user.addLinhTinh(item.id + 1, 1);
                            if ((item.id + 1) % 10 == 7) {
                                this.user.addMission(9, 1);
                            }
                            if ((item.id + 1) % 10 == 8) {
                                this.user.addMission(10, 1);
                            }
                            if ((item.id + 1) % 10 == 9) {
                                this.user.addMission(11, 1);
                            }
                        } else {
                            this.user.addLinhTinh(item.id, -1);
                        }
                    }
                    if (count > 0) {
                        if (this.user.session != null) {
                            this.user.session.sessionHandler
                                    .log(String.format(__("Chúc mừng, bạn đã nhập thành công %d viên %s"), count,
                                            LinhTinh.get(item.id + 1).name));
                        }
                    } else {
                        if (this.user.session != null) {
                            this.user.session.sessionHandler.log(__("Chúc bạn may mắn lần sau."));
                        }
                    }
                }
            }
            case 5 -> { // Phục hồi điểm nâng cấp
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null && array[1] > 0) {
                    if (array[1] > item.num) {
                        array[1] = item.num;
                    }
                    // Reset điểm nâng cấp của nhân vật
                    this.user.glass().resetUpgradePoints();
                    this.user.addLinhTinh(item.id, -array[1]);
                    if (this.user.session != null) {
                        this.user.session.sessionHandler.log(__("Đã reset điểm nâng cấp thành công."));
                        this.user.session.sessionHandler.loadInfo();
                    }
                }
            }
            case 6 -> { // Kinh nghiệm x2
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null && array[1] > 0) {
                    if (array[1] > item.num) {
                        array[1] = item.num;
                    }
                    this.user.timeX2EXP = System.currentTimeMillis() + 86400000 * array[1];
                    // Áp dụng buff kinh nghiệm x2 trong thời gian nhất định
                    // TODO: Implement experience boost system
                    this.user.addLinhTinh(item.id, -array[1]);
                    if (this.user.session != null) {
                        this.user.session.sessionHandler
                                .log(String.format(__("Đã kích hoạt kinh nghiệm x2 trong %d ngày."), array[1]));
                    }
                }
            }
            case 7 -> { // Học công thức chế tạo
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null && array[1] > 0) {
                    if (array[1] > item.num) {
                        array[1] = item.num;
                    }
                    // TODO: Add recipe to user's learned recipes
                    this.user.addLinhTinh(item.id, -array[1]);
                    if (this.user.session != null) {
                        this.user.session.sessionHandler
                                .log(String.format(__("Đã học thành công công thức: %s"), item.name));
                    }
                }
            }
            case 8 -> { // Bán nguyên liệu chế tạo
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null && array[1] > 0) {
                    if (array[1] > item.num) {
                        array[1] = item.num;
                    }
                    this.user.addLinhTinh(item.id, -array[1]);
                    this.user.addXu(item.xu / 2 * array[1], true);
                    if (this.user.session != null) {
                        this.user.session.sessionHandler.log(String.format(__("Đã bán %d %s, thu được %d xu."),
                                array[1], item.name, item.xu / 2 * array[1]));
                    }
                }
            }
            case 9 -> { // Bán vật phẩm đặc biệt khác
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null && array[1] > 0) {
                    if (array[1] > item.num) {
                        array[1] = item.num;
                    }
                    this.user.addLinhTinh(item.id, -array[1]);
                    this.user.addXu(item.xu / 2 * array[1], true);
                    if (this.user.session != null) {
                        this.user.session.sessionHandler.log(String.format(__("Đã bán %d %s, thu được %d xu."),
                                array[1], item.name, item.xu / 2 * array[1]));
                    }
                }
            }
            case 10 -> { // Sử dụng vật phẩm không thể bán
                int[] array = (int[]) this.object;
                LinhTinh item = this.user.getLinhTinh(array[0]);
                if (item != null && array[1] > 0) {
                    if (array[1] > item.num) {
                        array[1] = item.num;
                    }
                    // TODO: Implement specific use logic for each item
                    this.user.addLinhTinh(item.id, -array[1]);
                    if (this.user.session != null) {
                        this.user.session.sessionHandler
                                .log(String.format(__("Đã sử dụng %d %s."), array[1], item.name));
                    }
                }
            }
            case 11 -> { // Tăng kinh nghiệm cho thú cưng
                this.user.lockItem.lock();
                try {
                    int[] array = (int[]) this.object;
                    LinhTinh item = this.user.getLinhTinh(array[0]);
                    if (item != null && array[1] > 0) {
                        LinhTinh[] equipPet = this.user.getEquipPet();
                        if (equipPet[0] == null) {
                            this.user.session.sessionHandler.log(__("Bạn chưa trang bị thú cưng."));
                            return;
                        }
                        this.user.addLinhTinh(item.id, -array[1]);
                        int num = 0;
                        for (int i = 0; i < array[1]; i++) {
                            if (equipPet[0].level >= 30) {
                                int add = array[1] - num;
                                if (add > 0) {
                                    this.user.addLinhTinh(item.id, add);
                                }
                                this.user.session.sessionHandler.log(__("Thú cưng đã đạt cấp tối đa."));
                                return;
                            }
                            equipPet[0].addExp(10);
                            if (equipPet[0].isCanUpgrade()) {
                                LinhTinh newPet = LinhTinh.get(equipPet[0].id + 1);
                                newPet.level = equipPet[0].level + 1;
                                newPet.exp = 0;
                                newPet.expMax = newPet.level * 1000;
                                newPet.ability = equipPet[0].ability;
                                newPet.upgrade();
                                this.user.linhtinhs.add(newPet);
                                this.user.session.sessionHandler.addLinhTinh(newPet, 1);
                                this.user.addLinhTinh(equipPet[0].id, -equipPet[0].num);
                                newPet.isUse = true;
                                equipPet[0] = newPet;
                            } else if (equipPet[0].isCanLevelUp() && equipPet[0].level < 30) {
                                equipPet[0].levelUp();
                            }
                            num++;
                        }
                        this.user.session.sessionHandler.reloadPet();
                        this.user.session.sessionHandler.updateEquipPet();
                        this.user.session.sessionHandler.updateRuong();
                        this.user.session.sessionHandler
                                .log(__(String.format("Thú cưng đã lên cấp %d! Kinh nghiệm: %d/%d", equipPet[0].level,
                                        equipPet[0].exp, equipPet[0].expMax)));

                    }
                } finally {
                    this.user.lockItem.unlock();
                }
            }
            case 12 -> { // Học kỹ năng cho thú cưng
                this.user.lockItem.lock();
                try {
                    int[] array = (int[]) this.object;
                    LinhTinh item = this.user.getLinhTinh(array[0]);
                    if (item != null && array[1] > 0) {
                        LinhTinh[] equipPet = this.user.getEquipPet();
                        if (equipPet[0] == null) {
                            this.user.session.sessionHandler.log(__("Bạn chưa trang bị thú cưng."));
                            return;
                        }
                        int itemSkill = LinhTinh.getItemSkillByLinhTinh(equipPet[0].id);
                        if (itemSkill != item.id) {
                            this.user.session.sessionHandler
                                    .log(__("Thú cưng không thể học kỹ năng của thú cưng khác."));
                            return;
                        }
                        int skillId = LinhTinh.getSkillByLinhTinh(item.id);
                        if (skillId == -1) {
                            this.user.session.sessionHandler.log(__("Thú cưng không thể học kỹ năng này."));
                            return;
                        }
                        if (equipPet[0].isHaveSkill(skillId)) {
                            this.user.session.sessionHandler.log(__("Thú cưng đã học kỹ năng này."));
                            return;
                        }
                        this.user.addLinhTinh(item.id, -array[1]);
                        equipPet[0].addSkill(skillId);
                        this.user.session.sessionHandler.reloadPet();
                        this.user.session.sessionHandler.updateEquipPet();
                        this.user.session.sessionHandler.updateRuong();
                        this.user.session.sessionHandler
                                .log(__(String.format("Thú cưng đã học kỹ năng %s!", Skill.get(skillId).name)));
                    }
                } finally {
                    this.user.lockItem.unlock();
                }
            }
        }
    }

}
