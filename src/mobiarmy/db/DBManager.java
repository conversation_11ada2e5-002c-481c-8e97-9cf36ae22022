package mobiarmy.db;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import mobiarmy.server.ConfigManager;
import mobiarmy.util.Log;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DBManager {

    private static DBManager instance = null;
    private HikariDataSource hikariDataSource;
    private List<ZConnection> connections;
    private final int timeOut;

    public static final int GAME = 0;
    public static final int LOGIN = 1;
    public static final int SAVE_DATA = 2;
    public static final int SERVER = 3;
    public static final int UPDATE = 4;
    public static final int GIFT_CODE = 5;
    public static final int LOAD_CHAR = 6;
    public static final int CREATE_CHAR = 7;

    public static DBManager getInstance() {
        if (instance == null) {
            instance = new DBManager();
        }
        return instance;
    }

    private DBManager() {
        timeOut = 10;
        connections = new ArrayList<>();
        connections.add(new ZConnection(timeOut));
        connections.add(new ZConnection(timeOut));
        connections.add(new ZConnection(timeOut));
        connections.add(new ZConnection(timeOut));
        connections.add(new ZConnection(timeOut));
        connections.add(new ZConnection(timeOut));
        connections.add(new ZConnection(timeOut));
        connections.add(new ZConnection(timeOut));
    }

    public Connection getConnection() throws SQLException {
        return this.hikariDataSource.getConnection();
    }

    public boolean start() {
        if (this.hikariDataSource != null) {
            Log.warn("DB Connection Pool has already been created.");
            return false;
        } else {
            try {
                ConfigManager serverConfig = ConfigManager.getInstance();
                HikariConfig config = new HikariConfig();
                config.setJdbcUrl(serverConfig.getDatabaseUrl());
                config.setDriverClassName("com.mysql.cj.jdbc.Driver");
                config.setUsername(serverConfig.getDatabaseUsername());
                config.setPassword(serverConfig.getDatabasePassword());
                config.addDataSourceProperty("minimumIdle", 30);
                config.addDataSourceProperty("maximumPoolSize", serverConfig.getDatabasePoolMaximum());
                config.setConnectionTimeout(serverConfig.getDatabaseConnectionTimeout());
                config.setLeakDetectionThreshold(300000);
                config.setIdleTimeout(120000);
                config.addDataSourceProperty("cachePrepStmts", "true");
                config.addDataSourceProperty("prepStmtCacheSize", "250");
                config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");

                this.hikariDataSource = new HikariDataSource(config);
                Log.info("DB Connection Pool has created.");
                return true;
            } catch (Exception e) {
                Log.error("DB Connection Pool Creation has failed.");
                return false;
            }
        }
    }

    public ArrayList<HashMap<String, Object>> convertResultSetToList(ResultSet rs) {
        ArrayList<HashMap<String, Object>> list = new ArrayList<HashMap<String, Object>>();
        try {
            ResultSetMetaData resultSetMetaData = rs.getMetaData();
            int count = resultSetMetaData.getColumnCount();
            while (rs.next()) {
                HashMap<String, Object> map = new HashMap<>();
                int index = 1;
                while (index <= count) {
                    int type = resultSetMetaData.getColumnType(index);
                    String name = resultSetMetaData.getColumnName(index);
                    switch (type) {
                        case -5: {
                            map.put(name, rs.getLong(index));
                            break;
                        }
                        case 6: {
                            map.put(name, rs.getFloat(index));
                            break;
                        }
                        case 12: {
                            map.put(name, rs.getString(index));
                            break;
                        }
                        case 1: {
                            map.put(name, rs.getString(index));
                            break;
                        }
                        case 4: {
                            map.put(name, rs.getInt(index));
                            break;
                        }
                        case 16: {
                            map.put(name, rs.getBoolean(index));
                            break;
                        }
                        case -7: {
                            map.put(name, rs.getByte(index));
                            break;
                        }

                        default: {
                            map.put(name, rs.getObject(index));
                            break;
                        }
                    }
                    ++index;
                }
                list.add(map);
            }
            rs.close();
        } catch (SQLException sQLException) {
            Log.error("convertResultSetToList ex: " + sQLException.getMessage());
        }
        return list;
    }

    public Connection getConnection(int index) throws SQLException {
        if (hikariDataSource == null) {
            while (hikariDataSource == null) {
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException ex) {
                    Logger.getLogger(DBManager.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
        return connections.get(index).getConnection();
    }

    public void shutdown() {
        try {
            if (this.hikariDataSource != null) {
                this.hikariDataSource.close();
                Log.info("DB Connection Pool is shutting down.");
            }
            this.hikariDataSource = null;
        } catch (Exception e) {
            Log.warn("Error when shutting down DB Connection Pool");
        }
    }

    public int update(String sql, Object... params) {
        try {
            Connection conn = getInstance().getConnection(UPDATE);
            PreparedStatement stmt = conn.prepareStatement(sql);
            try {
                for (int i = 0; i < params.length; i++) {
                    stmt.setObject(i + 1, params[i]);
                }
                int result = stmt.executeUpdate();
                return result;
            } finally {
                stmt.close();
            }
        } catch (SQLException e) {
            Log.error("update() EXCEPTION: " + e.getMessage(), e);
        }
        return -1;
    }
}
