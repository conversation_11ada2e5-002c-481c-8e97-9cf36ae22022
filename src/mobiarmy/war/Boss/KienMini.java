package mobiarmy.war.Boss;

import java.util.concurrent.TimeUnit;

import mobiarmy.util.Util;
import mobiarmy.war.Player;

public class <PERSON>enMini extends Boss {

    public KienMini(int hp, int att, int x, int y) {
        super("Kiến Con", (byte) 33, hp, att, 0, 0, 0, 27, 26, x, y);
    }

    @Override
    public void update() {
        super.update();
        // Đến l<PERSON> ch<PERSON>a
        if (super.index == super.mapData.getTurn() && System.currentTimeMillis() > super.mapData.timeUntilAction2) {
            // Bắn
            if (!super.isShoot) {
                Player player = super.mapData.getPlayerNear(super.index);
                if (player != null) {
                    super.updateXY(player.x, player.y);
                    super.isShoot = true;
                    if (Math.abs(super.x - player.x) < 50 && Math.abs(super.y - player.y) < 40) {
                        super.mapData.updateAttackBoss(super.index, (byte) 0);
                        Util.executorService.schedule(() -> {
                            int damage = Util.nextInt(super.att, super.att * 2);
                            if (player != null) {
                                player.updateHP(-damage);
                                super.mapData.updateHP(player.index, player.hp, player.pixel);
                            }
                            super.mapData.updateAffect(super.index);
                            super.mapData.isTurn = true;
                        }, 2, TimeUnit.SECONDS);
                    }else{
                        super.mapData.isTurn = true;
                    }
                }
            }
        }
    }
}