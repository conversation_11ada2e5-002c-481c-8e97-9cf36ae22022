package mobiarmy.war.Boss;

import java.util.concurrent.TimeUnit;

import mobiarmy.util.Util;
import mobiarmy.war.Player;

public class <PERSON><PERSON><PERSON> extends Boss {
    
    // <PERSON> dõi lịch sử tấn công để tránh lặp lại liên tục
    private int lastAttackType = -1;
    private int attackCount = 0;

    public TaThan(int hp, int att, int x, int y) {
        super("Tà Thần", (byte) 30, hp, att, 0, 0, 0, 65, 75, x, y);
    }

    @Override
    public void update() {
        super.update();
        // Đến lượt chưa
        if (super.index == super.mapData.getTurn() && System.currentTimeMillis() > super.mapData.timeUntilAction2) {
            // Bắn
            if (!super.isShoot) {
                Player player = super.mapData.getPlayerNear(super.index);
                if (player != null) {
                    int attackType = chooseAttackType();
                    super.mapData.updateAttackBoss(super.index, (byte) attackType);
                    super.isShoot = true;

                    Util.executorService.schedule(() -> {
                        executeAttack(attackType);
                        super.mapData.updateAffect(super.index);
                        super.mapData.isTurn = true;
                    }, attackType == 0 ? 2 : 3, TimeUnit.SECONDS);
                }
            }
        }
    }
    
    private int chooseAttackType() {
        // Ngẫu nhiên chọn giữa 3 loại tấn công nhưng tránh lặp lại liên tục
        int chosenAttack = chooseRandomAttackAvoidingRepeat();
        
        // Cập nhật lịch sử tấn công
        if (lastAttackType == chosenAttack) {
            attackCount++;
        } else {
            lastAttackType = chosenAttack;
            attackCount = 1;
        }
        
        return chosenAttack;
    }
    
    private int chooseRandomAttackAvoidingRepeat() {
        int chosenAttack;
        
        // Ngẫu nhiên chọn giữa 3 loại tấn công (0, 1, 2)
        chosenAttack = Util.nextInt(0, 3);
        
        // Kiểm tra xem có phải tấn công liên tục cùng loại không (chỉ áp dụng cho Đánh_2 và Đánh_3)
        if (lastAttackType == chosenAttack && attackCount >= 1 && (chosenAttack == 1 || chosenAttack == 2)) {
            // Nếu vừa chọn cùng loại (Đánh_2 hoặc Đánh_3), chọn loại khác
            if (chosenAttack == 1) {
                // Nếu vừa chọn Đánh_2, chọn ngẫu nhiên giữa Đánh_1 và Đánh_3
                chosenAttack = Util.nextInt(0, 1) == 0 ? 0 : 2;
            } else if (chosenAttack == 2) {
                // Nếu vừa chọn Đánh_3, chọn ngẫu nhiên giữa Đánh_1 và Đánh_2
                chosenAttack = Util.nextInt(0, 1) == 0 ? 0 : 1;
            }
        }
        
        return chosenAttack;
    }
    
    
    private void executeAttack(int attackType) {
        switch (attackType) {
            case 0: // Đánh_1: Gây st tất cả người chơi, nếu có người ở gần thì đánh chết luôn
                executeAttack1();
                break;
            case 1: // Đánh_2: Hồi máu cho bản thân
                executeAttack2();
                break;
            case 2: // Đánh_3: Tăng damage và thủ
                executeAttack3();
                break;
        }
    }
    
    private void executeAttack1() {
        // Gây sát thương tất cả người chơi
        for (Player player : super.mapData.players) {
            if (player != null && !player.isDie && !player.isBoss) {
                // Kiểm tra khoảng cách
                int distance = Math.abs(player.x - super.x) + Math.abs(player.y - super.y);
                
                if (distance < 100) {
                    // Nếu ở gần thì đánh chết luôn
                    player.updateHP(-player.hp);
                } else {
                    // Nếu xa thì gây sát thương bình thường
                    int damage = Util.nextInt(super.att, super.att * 2);
                    player.updateHP(-damage);
                }
                super.mapData.updateHP(player.index, player.hp, player.pixel);
            }
        }
    }
    
    private void executeAttack2() {
        // Hồi máu cho bản thân
        int healAmount = Util.nextInt(500, 800);
        super.hp += healAmount;
        // Đảm bảo không vượt quá máu tối đa
        if (super.hp > super.hpMax) {
            super.hp = super.hpMax;
        }
        super.mapData.updateHP(super.index, super.hp, super.pixel);
    }
    
    private void executeAttack3() {
        // Tăng damage và thủ cho bản thân
        // Tăng attack (damage)
        super.att += 50;
        // Tăng defense (thủ)
        super.def += 30;
        super.mapData.chat(super.userID, "Than linh ban ban phuoc");
        // Có thể thêm hiệu ứng visual hoặc thông báo
        // super.mapData.updateBuff(super.index, (byte) 3); // Nếu có method này
    }
}
