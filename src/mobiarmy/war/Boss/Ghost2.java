package mobiarmy.war.Boss;

import mobiarmy.util.Util;
import mobiarmy.war.Player;

public class Ghost2 extends Boss {

    public Ghost2(int hp, int att, int x, int y) {
        super("Ghost II", (byte) 26, hp, att, 0, 0, 0, 35, 31, x, y);
        super.isFly = true;
    }

    @Override
    public void update() {
        super.update();
        // Đ<PERSON><PERSON>
        if (super.index == super.mapData.getTurn() && System.currentTimeMillis() > super.mapData.timeUntilAction2) {
            // Bắn
            if (!super.isShoot) {
                // T<PERSON>m đ<PERSON>i thủ gần
                Player player = super.mapData.getPlayerNear(super.index);
                if (player != null) {
                    if (super.x > player.x) {
                        super.x = (short) (player.x + 30);
                    } else {
                        super.x = (short) (player.x - 30);
                    }
                    super.y = (short) (player.y - 15);
                    super.mapData.changeLocationFly(super.index, super.x, super.y);
                    super.mapData.ghostBullet(super.index, player.index);
                    short wmap = super.mapData.width;
                    short hmap = super.mapData.height;
                    super.x = (short) ((super.x > player.x && player.x < wmap - 80) ? (player.x + 80)
                            : ((player.x > 80) ? (player.x - 80) : (player.x + 80)));
                    super.y = (short) Util.nextInt(0, hmap - 200);
                    super.mapData.changeLocationFly(super.index, super.x, super.y);
                    player.updateHP(-Util.nextInt(300, 500));
                    super.mapData.updateHP(player.index, player.hp, player.pixel);
                    super.mapData.updateAffect(super.index);
                    super.isShoot = true;
                    super.mapData.isTurn = true;
                }
                super.mapData.updateGhost2();
            }
        }
    }
}
