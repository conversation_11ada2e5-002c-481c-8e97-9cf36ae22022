package mobiarmy.war.Boss;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

import mobiarmy.util.Util;
import mobiarmy.war.Player;

public class NuVuongKien extends Boss {
    public NuVuongKien(int hp, int att, int x, int y) {
        super("Kiến <PERSON>", (byte) 31, hp, att, 0, 0, 0, 60, 90, x, y);
    }

    @Override
    public void update() {
        super.update();
        // Đến l<PERSON>ợt chưa
        if (super.index == super.mapData.getTurn() && System.currentTimeMillis() > super.mapData.timeUntilAction2) {
            // Bắn
            if (!super.isShoot) {
                Player player = super.mapData.getPlayerNear(super.index);
                if (player != null) {
                    int attackType = chooseAttackType();
                    super.mapData.updateAttackBoss(super.index, (byte) attackType);
                    super.isShoot = true;

                    Util.executorService.schedule(() -> {
                        executeAttack(attackType);
                        super.mapData.updateAffect(super.index);
                        super.mapData.isTurn = true;
                    }, 2, TimeUnit.SECONDS);
                }
            }
        }
    }
    
    private int chooseAttackType() {
        // Kiểm tra có player nào bị đóng băng không
        boolean hasFrozenPlayer = hasFrozenPlayer();
        // Kiểm tra có player nào trong khoảng cách 100 không
        boolean hasNearPlayer = hasNearPlayer();
        
        // Đánh_1: Dùng khi có player bị đóng băng hoặc có player trong khoảng cách 100
        if (hasFrozenPlayer || hasNearPlayer) {
            return 0; // Đánh_1 - 1 hit chết luôn
        }
        
        // Ngẫu nhiên chọn giữa Đánh_2 và Đánh_3
        return Util.nextInt(1, 3);
//        return 2;
    }
    
    private boolean hasFrozenPlayer() {
        for (Player player : super.mapData.players) {
            if (player != null && !player.isDie && !player.isBoss && player.countFreeze > 0) {
                return true;
            }
        }
        return false;
    }
    
    private boolean hasNearPlayer() {
        for (Player player : super.mapData.players) {
            if (player != null && !player.isDie && !player.isBoss) {
                int distance = Math.abs(player.x - super.x) + Math.abs(player.y - super.y);
                if (distance < 100) {
                    return true;
                }
            }
        }
        return false;
    }
    
    private void executeAttack(int attackType) {
        switch (attackType) {
            case 0: // Đánh_1: 1 hit chết luôn
                executeAttack1();
                break;
            case 1: // Đánh_2: Gây sát thương 3 mục tiêu ngẫu nhiên
                executeAttack2();
                break;
            case 2: // Đánh_3: Đóng băng 1 mục tiêu ngẫu nhiên
                executeAttack3();
                break;
        }
    }
    
    private void executeAttack1() {
        // Tìm player trong khoảng cách 100 hoặc đang bị đóng băng
        Player target = findTargetForAttack1();
        if (target != null) {
            target.updateHP(-target.hp); // 1 hit chết luôn
            super.mapData.updateHP(target.index, target.hp, target.pixel);
        }
    }
    
    private Player findTargetForAttack1() {
        // Ưu tiên tìm player bị đóng băng trước (không cần gần)
        Player frozenTarget = findFrozenPlayer();
        if (frozenTarget != null) {
            return frozenTarget;
        }
        
        // Nếu không có player bị đóng băng, tìm player trong khoảng cách 100
        return findNearPlayer();
    }
    
    private Player findFrozenPlayer() {
        // Tìm player bị đóng băng bất kỳ (không cần gần)
        for (Player player : super.mapData.players) {
            if (player != null && !player.isDie && !player.isBoss && player.countFreeze > 0) {
                return player;
            }
        }
        return null;
    }
    
    private Player findNearPlayer() {
        // Tìm player trong khoảng cách 100 pixel
        for (Player player : super.mapData.players) {
            if (player != null && !player.isDie && !player.isBoss) {
                int distance = Math.abs(player.x - super.x) + Math.abs(player.y - super.y);
                if (distance < 100) {
                    return player;
                }
            }
        }
        return null;
    }
    
    private void executeAttack2() {
        // Gây sát thương 3 mục tiêu ngẫu nhiên
        int targetsHit = 0;
        int maxTargets = 3;
        
        // Tạo danh sách player có thể tấn công
        ArrayList<Player> availableTargets = new ArrayList<>();
        for (Player player : super.mapData.players) {
            if (player != null && !player.isDie && !player.isBoss) {
                availableTargets.add(player);
            }
        }
        
        // Tấn công ngẫu nhiên 3 mục tiêu
        while (targetsHit < maxTargets && !availableTargets.isEmpty()) {
            int randomIndex = Util.nextInt(availableTargets.size());
            Player target = availableTargets.remove(randomIndex);
            
            int damage = Util.nextInt(300, 600);
            target.updateHP(-damage);
            super.mapData.updateHP(target.index, target.hp, target.pixel);
            targetsHit++;
        }
    }
    
    private void executeAttack3() {
        // Đóng băng 1 mục tiêu ngẫu nhiên
        ArrayList<Player> availableTargets = new ArrayList<>();
        for (Player player : super.mapData.players) {
            if (player != null && !player.isDie && !player.isBoss) {
                availableTargets.add(player);
            }
        }
        
        if (!availableTargets.isEmpty()) {
            int randomIndex = Util.nextInt(availableTargets.size());
            Player target = availableTargets.get(randomIndex);
            
            // Đóng băng player
            target.nextFreeze = true;
            super.mapData.freeze(target.index, 0);
        }
    }
}
