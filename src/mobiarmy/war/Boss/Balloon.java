package mobiarmy.war.Boss;

import mobiarmy.util.Util;
import mobiarmy.war.Player;

public class Balloon extends Boss {
    private byte[] part;

    public Balloon(int hp, int att, int x, int y) {
        super("Balloon", (byte) 17, hp, att, 0, 0, 0, 138, 18, x, y);
        super.isFly = true;
        super.theluc = 40;
    }

    @Override
    public void update() {
        super.update();

        // Initialize part array for all Balloon components
        if (part == null) {
            byte[] dem = new byte[5];
            if (super.mapData != null) {
                for (int i = 0; i < super.mapData.players.length; i++) {
                    if (super.mapData.players[i] != null) {
                        if (super.mapData.players[i].name.equals("Balloon Gun")) {
                            dem[1] = super.mapData.players[i].index;
                        }
                        if (super.mapData.players[i].name.equals("Balloon Gun Big")) {
                            dem[2] = super.mapData.players[i].index;
                        }
                        if (super.mapData.players[i].name.equals("<PERSON> Back")) {
                            dem[3] = super.mapData.players[i].index;
                        }
                    }
                }
                dem[0] = super.index; // Main Balloon
                dem[4] = (byte) -1; // Balloon Eye (not spawned yet)
                part = dem;
            }
        }

        // Đến lượt chưa
        if (super.index == super.mapData.getTurn() && System.currentTimeMillis() > super.mapData.timeUntilAction2) {
            // Bắn
            if (!super.isShoot) {
                Player pl = super.mapData.getPlayerNear(super.index);
                if (pl == null || part == null) {
                    super.mapData.isTurn = true;
                    return;
                }
                int[] turns = { 0, 2, 4 };
                int turn = turns[Util.nextInt(0, turns.length - 1)];
                boolean check = false;
                for (int i = 0; i < part.length; i++) {
                    if (part[i] == -1) {
                        continue;
                    }
                    Player boss = super.mapData.players[part[i]];
                    if (boss != null && !boss.isDie && (i == 1 || i == 2 || i == 4)) {
                        check = true;
                    }
                }
                if (((super.mapData.players[part[1]].isDie && super.mapData.players[part[2]].isDie)
                        || super.mapData.players[part[3]].isDie) && part[4] == -1) {
                    Player eya = new Balloon_Eye(1500, 700, super.x + 55, super.y - 27);
                    super.mapData.addBoss(eya);
                    part[4] = eya.index;
                }
                if (!check) {
                    super.mapData.players[part[0]].isDie = true;
                    super.mapData.players[part[0]].hp = 0;
                    super.mapData.updateHP(super.mapData.players[part[0]].index, super.mapData.players[part[0]].hp,
                            super.mapData.players[part[0]].pixel);
                    super.mapData.players[part[3]].isDie = true;
                    super.mapData.players[part[3]].hp = 0;
                    super.mapData.updateHP(super.mapData.players[part[3]].index, super.mapData.players[part[3]].hp,
                            super.mapData.players[part[3]].pixel);
                    super.mapData.updateAffect(super.index);
                    super.mapData.isTurn = true;
                    super.mapData.updateComplete();
                } else {
                    if (turn == 0) {
                        // short toX = (short) Util.nextInt(100, super.mapData.width - 100);
                        // short toY = (short) Util.nextInt(-150, 50);
                        // for (int i = 0; i < part.length; i++) {
                        //     if (part[i] == -1) {
                        //         continue;
                        //     }
                        //     Player boss = super.mapData.players[part[i]];
                        //     if (boss != null && !boss.isDie) {
                        //         switch (i) {
                        //             case 0:
                        //                 boss.x = toX;
                        //                 boss.y = toY;
                        //                 break;
                        //             case 1:
                        //                 boss.x = (short) (toX + 51);
                        //                 boss.y = (short) (toY + 19);
                        //                 break;
                        //             case 2:
                        //                 boss.x = (short) (toX - 5);
                        //                 boss.y = (short) (toY + 30);
                        //                 break;
                        //             case 3:
                        //                 boss.x = (short) (toX - 67);
                        //                 boss.y = (short) (toY - 6);
                        //                 break;
                        //             case 4:
                        //                 boss.x = (short) (toX + 57);
                        //                 boss.y = (short) (toY - 27);
                        //                 break;
                        //         }
                        //         super.mapData.changeLocationFly(boss.index, boss.x, boss.y);
                        //     }
                        // }
                        if (!super.mapData.players[part[1]].isDie) {
                            super.mapData.shootBullet(super.index, false, 44, super.x, super.y, super.width,
                                    super.height, (int) Util.getArgXY(super.x, super.y, pl.x, pl.y), 10, 0, 1, 300, -1);
                            super.mapData.updateAffect(super.index);
                            super.mapData.isTurn = true;
                            super.isShoot = true;
                        } else {
                            super.mapData.isTurn = true;
                        }
                    } else if (turn == 2) {
                        if (!super.mapData.players[part[2]].isDie) {
                            super.mapData.shootBullet(super.index, false, 43, super.x, super.y, super.width,
                                    super.height, 270, 20, 0, 1, 300, -1);
                            super.mapData.updateAffect(super.index);
                            super.mapData.isTurn = true;
                            super.isShoot = true;
                        } else {
                            super.mapData.isTurn = true;
                        }
                    } else if (turn == 4) {
                        if (!super.mapData.players[part[4]].isDie) {
                            super.mapData.shootBullet(super.index, false, 45, super.x, super.y, super.width,
                                    super.height, (int) Util.getArgXY(super.x, super.y, pl.x, pl.y), 20, 0, 1, 300, -1);
                            super.mapData.updateAffect(super.index);
                            super.isShoot = true;
                        } else {
                            super.mapData.isTurn = true;
                        }
                    }
                }
            }
        }
    }
}
