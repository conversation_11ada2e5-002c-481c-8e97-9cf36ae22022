package mobiarmy.war.Boss;

import mobiarmy.util.Util;
import mobiarmy.war.Player;
import mobiarmy.war.Boss.bullet.BulletTrajectory;

public class SpiderPoisonous extends Boss {

    public SpiderPoisonous(int hp, int att, int x, int y) {
        super("Spider Poisonous", (byte) 22, hp, att, 0, 0, 0, 45, 48, x, y);
        super.isFly = true;
    }

    @Override
    public void update() {
        super.update();
        // <PERSON><PERSON><PERSON>
        try {
            if (super.index == super.mapData.getTurn() && System.currentTimeMillis() > super.mapData.timeUntilAction2) {
                if (!super.isShoot) {
                    if (super.trajectory == null) {
                        // T<PERSON><PERSON> đ<PERSON>i thủ gần
                        Player player = super.mapData.getPlayerNear(super.index);
                        if (player != null) {
                            if (!player.isPoison) {
                                short xpre = super.x;
                                short ypre = super.y;
                                super.changeLocationFly(player.x, player.y);
                                super.mapData.sendPoison(super.index, (byte) player.index);
                                player.isPoison = true;
                                player.addPoison(super.index, 150);
                                super.mapData.poison(player.index);
                                super.mapData.isTurn = true;
                                super.isShoot = true;
                                super.x = xpre;
                                super.y = ypre;
                                super.changeLocationFly(super.x, super.y);
                            } else if (Math.abs(super.x - player.x) < 40 && Math.abs(super.y - player.y) < 40) {
                                super.mapData.shootBullet(super.index, false, 8, super.x, super.y, super.width,
                                        super.height, Util.nextInt(180, 360), Util.nextInt(1, 5), 0, 1, super.att, -1);
                                super.mapData.updateAffect(super.index);
                                super.mapData.isTurn = true;
                                super.isShoot = true;
                            } else {
                                int arrBulletId[] = new int[] { 8, 47 };
                                int bID = arrBulletId[Util.nextInt(arrBulletId.length)];
                                this.trajectory = new BulletTrajectory(super.mapData, super.index, bID, super.x,
                                        super.y,
                                        super.width, super.height, player.x - player.width / 2,
                                        player.y - player.height,
                                        player.width, player.height, bID == 8 ? -45 : 60, bID == 8 ? 1 : 10, bID == 8,
                                        true);
                                this.trajectory.start();
                            }
                        }else{
                            super.mapData.isTurn = true;
                        }
                    } else if (super.trajectory.complate) {
                        if (super.trajectory.place) {
                            super.mapData.shootBullet(super.index, false, super.trajectory.bulletId, super.x, super.y,
                                    super.width, super.height, super.trajectory.ang, super.trajectory.force,
                                    super.trajectory.force2, 1, super.att, -1);
                            if (super.trajectory.bulletId == 8) {
                                super.mapData.shootBullet(super.index, false, 36, super.x, super.y, super.width,
                                        super.height, Util.nextInt(45, 135), Util.nextInt(3, 10), 0, 1, super.att, -1);
                            }
                            super.mapData.updateAffect(super.index);
                            super.isShoot = true;
                            super.mapData.isTurn = true;
                        } 
                        super.mapData.isTurn = true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
