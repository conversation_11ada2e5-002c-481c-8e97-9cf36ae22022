package mobiarmy.util;

import java.text.NumberFormat;
import java.util.Locale;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;

/**
 *
 * <AUTHOR> Tú
 */
public class Util {
    public static ScheduledExecutorService executorService = Executors.newScheduledThreadPool(500);
    private static final NumberFormat numberFormat = NumberFormat.getInstance(new Locale("vi"));

    public static int nextInt(int min, int max) {
        if (min == max) return min;
        if (min > max) {
            return ThreadLocalRandom.current().nextInt(max, min);
        }
        return ThreadLocalRandom.current().nextInt(min, max);
    }

    public static <T> T nextT(T ...array) {
        return array[Util.nextInt(array.length)];
    }
    
    public static double nextDouble() {
        return ThreadLocalRandom.current().nextDouble();
    }
    
    public static int nextInt(int bound) {
        return ThreadLocalRandom.current().nextInt(bound);
    }
    
    public static String formatNum(int num) {
        return String.format("%,d", num).replace(",", ".");
    }
    
    public static String getRandomCharacters(String inputString, int numberOfChars) {
        if (numberOfChars > inputString.length()) {
            numberOfChars = inputString.length();
        }
        
        StringBuilder result = new StringBuilder(numberOfChars);
        
        for (int i = 0; i < numberOfChars; i++) {
            int randomIndex = nextInt(inputString.length());
            result.append(inputString.charAt(randomIndex));
        }
        
        return result.toString();
    }
    public static float getArgXY(float Ax, float Ay, float Bx, float By) {
        float K = Math.abs(Ay - By);
        float D = Math.abs(Ax - Bx);
        float tan = (D / K);
        float IntArg = (float) (Math.toDegrees(Math.atan(tan)));
        if (Ax >= Bx && Ay > By) {
            IntArg += 90;
        } else if (Ax > Bx && Ay <= By) {
            IntArg = (270 - IntArg);
        } else if (Ax <= Bx && Ay < By) {
            IntArg -= 90;
        } else if (Ax < Bx && Ay >= By) {
            IntArg = (270 + IntArg);
        }
        return IntArg;
    }
    public static void setTimeout(Runnable runnable, int delay) {
        new Thread(() -> {
            try {
                Thread.sleep(delay);
                runnable.run();
            } catch (Exception e) {
                System.err.println(e);
            }
        }).start();
    }
    public static String getCurrency(long number) {
        return numberFormat.format(number);
    }

}
